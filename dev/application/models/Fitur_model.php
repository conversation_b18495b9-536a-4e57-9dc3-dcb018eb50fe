<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Fitur_model extends My_Model{

	function __construct(){
		parent::__construct();
	}

	function table_query()
  {
    $this->db->select('IF(mu.ID IS NULL, "", mu.ID) ID, a.ID IDM, a.DESKRIPSI LABEL, IF(mu.`STATUS` = 1 , "checked","" ) STATUS');
    $this->db->from('remun_medis.fitur a');
    $this->db->join('remun_medis.fitur_user mu','a.ID = mu.ID_FITUR AND mu.ID_USER = "'.$this->input->post('id').'"','LEFT');
    $this->db->order_by('IDM');
  
  }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

	function menu($parent = 0){
		$this->db->select('m.*');
		$this->db->from('remun_medis.menu_user mu');
		$this->db->join('remun_medis.menu m','mu.ID_MENU = m.ID');
		$this->db->where(array('mu.ID_USER'=>$_SESSION['id'],'m.PARENT'=>$parent,'mu.STATUS !=' => 0,'m.MODULE'=>$_SESSION['module']));
		$this->db->order_by('m.SEQ');
		
		$query = $this->db->get();
		return $query;

	}

}
