<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Info_pasien_model extends My_Model{
    protected $_table_name = 'remun_medis.log_info_pasien';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'ASC';

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('ip.ID, j.ID ID_JADWAL, `master`.getNamaLengkapPegawai(d.NIP) DOKTER, r.DESKRIPSI RUANGAN
        ,j.TANGGAL, ip.PESAN, ip.TANGGAL WAKTU_INPUT');
        $this->db->from('remun_medis.log_info_pasien ip');
		$this->db->join('remun_medis.jadwal j', 'ip.ID_JADWAL = j.ID','LEFT');
		$this->db->join('`master`.dokter d', 'j.DOKTER = d.ID','LEFT');
		$this->db->join('`master`.ruangan r', 'j.RUANGAN = r.ID','LEFT');
        $this->db->where(array('j.DOKTER' => $this->input->post('dokter'), 'j.TANGGAL' => $this->input->post('awal'),'j.RUANGAN' => $this->input->post('ruangan'),'j.STATUS !=' => 0));
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }
}
