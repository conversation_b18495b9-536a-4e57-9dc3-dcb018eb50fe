<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Detil_info_pasien_model extends My_Model{
    protected $_table_name = 'remun_medis.log_detil_info_pasien';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'ASC';

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $this->db->select('p.NOMR, p.NAMAPASIEN, dip.`STATUS`');
        $this->db->from('remun_medis.log_detil_info_pasien dip');
		$this->db->join('remun_medis.perjanjian p', 'dip.ID_PERJANJIAN = p.ID','LEFT');
        $this->db->where('dip.ID_INFO', $this->input->post('id'));
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }
}
