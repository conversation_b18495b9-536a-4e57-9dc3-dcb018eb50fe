<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jadwal_model extends My_Model{
	protected $_table_name = 'remun_medis.jadwal';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'ASC';

	public $rules = array(
			'jdokter' => array(
				'field' => 'jdokter',
				'rules' => 'trim|required'
			),
			'jruangan' => array(
				'field' => 'jruangan',
				'rules' => 'trim|required'
			),
			'jtanggal' => array(
				'field' => 'jtanggal',
				'rules' => 'trim|required'
			),
			// 'awal' => array(
			// 	'field' => 'awal',
			// 	'rules' => 'trim|required'
			// ),
			// 'akhir' => array(
			// 	'field' => 'akhir',
			// 	'rules' => 'trim|required'
			// ),
			// 'kuota' => array(
			// 	'field' => 'kuota',
			// 	'rules' => 'trim|required'
			// ),
    );
    
    public $rules_multiple = array(
        'jdokter[]' => array(
            'field' => 'jdokter[]',
            'rules' => 'trim|required'
        ),
        'jruangan[]' => array(
            'field' => 'jruangan[]',
            'rules' => 'trim|required'
        ),
        'jtanggal[]' => array(
            'field' => 'jtanggal[]',
            'rules' => 'trim|required'
        ),
    );

    public $rules_pagi = array(
        'awal' => array(
            'field' => 'awal',
            'rules' => 'trim|required'
        ),
        'akhir' => array(
            'field' => 'akhir',
            'rules' => 'trim|required'
        ),
        'kuota' => array(
            'field' => 'kuota',
            'rules' => 'trim|required'
        ),
        'detail_awal[]' => array(
            'field' => 'detail_awal[]',
            'rules' => 'trim|required'
        ),
        'detail_akhir[]' => array(
            'field' => 'detail_akhir[]',
            'rules' => 'trim|required'
        ),
        'detail_kuota[]' => array(
            'field' => 'detail_kuota[]',
            'rules' => 'trim|required'
        ),
    );

    public $rules_sore = array(
        'awal_sore' => array(
            'field' => 'awal_sore',
            'rules' => 'trim|required'
        ),
        'akhir_sore' => array(
            'field' => 'akhir_sore',
            'rules' => 'trim|required'
        ),
        'kuota_sore' => array(
            'field' => 'kuota_sore',
            'rules' => 'trim|required'
        ),
    );

	function __construct(){
		parent::__construct();
	}

    public function jadwal()
	{
        $getPenjamin = $this->getPenjamin();
        // echo $getPenjamin;
		$this->db->select('rj.ID,`master`.getNamaLengkapPegawai(md.NIP) PEGAWAI,mr.ID ID_RUANGAN,mr.DESKRIPSI,DATE_FORMAT(rj.TANGGAL,"%d") DAY,rj.TANGGAL,CONCAT(rj.AWAL,"-",rj.AKHIR) WAKTU,  SUM(CASE WHEN rp.status != 0 AND rp.STATUS_SORE = 0 THEN 1 ELSE 0 END) AS JUMLAH, rj.KUOTA, CONCAT(rj.AWAL_SORE,"-",rj.AKHIR_SORE) WAKTU_SORE, SUM(CASE WHEN rp.status != 0 AND rp.STATUS_SORE = 1 THEN 1 ELSE 0 END) AS JUMLAH_SORE, rj.KUOTASORE, rr.COLOR, DAYNAME(rj.TANGGAL) HARI');
        $this->db->from('remun_medis.jadwal rj');
        $this->db->join('`master`.dokter md','rj.DOKTER = md.ID');
        $this->db->join('`master`.ruangan mr','rj.RUANGAN = mr.ID');
        $this->db->join('`remun_medis`.ruangan rr','mr.ID = rr.ID','LEFT');
		
        if($this->input->post('dokter') == 114){
            $this->db->join('remun_medis.perjanjian rp','rj.DOKTER = rp.ID_DOKTER AND rj.RUANGAN = rp.ID_RUANGAN AND rj.TANGGAL = rp.TANGGAL','LEFT');
        }else{
            $this->db->join('remun_medis.perjanjian rp','rj.DOKTER = rp.ID_DOKTER AND rj.RUANGAN = rp.ID_RUANGAN AND rj.TANGGAL = rp.TANGGAL AND rp.RENCANA IN(0,1)','LEFT');
        }
 
		$this->db->where('DATE_FORMAT(rj.TANGGAL, "%Y-%m") ="'. $this->input->post('bulan').'"');
        $this->db->where('rj.TANGGAL >= CURDATE()');
        $this->db->where('rj.STATUS != 0');
        $this->db->where_not_in('mr.ID', array('105020203','105130104'));
        if($getPenjamin != 2 && !$this->input->post('poli')) {
            $this->db->where_not_in('mr.ID', array('105020704','105020705'));
        }else{
            if($this->input->post('kios')){
                $this->db->where('mr.GEDUNG IS NULL');
                $this->db->where_not_in('mr.ID', array('105020201','105020202'));
            }
        }


        $this->db->order_by('rj.RUANGAN DESC, rj.TANGGAL ASC');
        $this->db->group_by(array('rj.TANGGAL', 'rj.RUANGAN'));

        if($this->input->post('dokter')){
        	$this->db->where('md.ID',$this->input->post('dokter'));
        }

        $query = $this->db->get();
  		
  		return $query->result();
	}

	function cek(){
        $this->db->select('rj.ID');
        $this->db->from('remun_medis.jadwal rj');
        $this->db->where(array('rj.DOKTER' => $this->input->post('jdokter'), 'rj.RUANGAN' => $this->input->post('jruangan'), 'rj.TANGGAL' => $this->input->post('jtanggal'), 'rj.STATUS !=' => 0));
        $query = $this->db->get();
        $row = $query->row();
        $num = $query->num_rows();

        if($num > 0){
            return array('status' => 503, 'message' => 'Data Sudah Ada', 'id' => $row -> ID);
        }
        return array('status' => 200, 'message' => 'Success');

    }

    function get_table($single = TRUE){
        $this->db->select('rj.ID,rj.DOKTER,rj.RUANGAN IDRUANGAN,rp.DESKRIPSI,rj.TANGGAL,rj.AWAL,rj.AKHIR,rj.KUOTA,rj.KUOTASORE,rj.AWAL_SORE,rj.AKHIR_SORE, rj.LOCK');
        $this->db->from('remun_medis.jadwal rj');
        $this->db->join('master`.ruangan rp','rj.RUANGAN = rp.ID');
        $this->db->where('rj.ID',$this->input->post('id'));

        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
	}
	
	function calendar($single = TRUE){
        $start = $this->input->get('start');
        $end = $this->input->get('end');
        $this->db->select('mj.ID, mr.DESKRIPSI, CONCAT(mj.TANGGAL,"T",STR_TO_DATE(IF(mj.AWAL IS NULL, mj.AWAL_SORE,mj.AWAL),"%H:%i:%s")) START, CONCAT(mj.TANGGAL,"T",STR_TO_DATE(IF(mj.AKHIR IS NULL, mj.AKHIR_SORE,mj.AKHIR),"%H:%i:%s")) END, mj.KUOTA,mr.COLOR');
        $this->db->from('remun_medis.jadwal mj');
        $this->db->join('remun_medis.ruangan mr','mj.RUANGAN = mr.ID');
        // $this->db->join('remun_medis.sesi ms','mj.SESI = ms.ID','LEFT');
        $this->db->where('mj.DOKTER', $this->uri->segment(3));
        $this->db->where("mj.TANGGAL BETWEEN '$start' AND '$end'");
        $this->db->where('mj.STATUS !=', 0);
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_jadwal($dokter,$ruangan,$tanggal){
        $this->db->select('*');
        $this->db->from('remun_medis.jadwal mj');
        $this->db->where('mj.DOKTER', $dokter);
        $this->db->where('mj.RUANGAN', $ruangan);
        $this->db->where('mj.TANGGAL', $tanggal);
        $this->db->where('mj.STATUS !=', 0);
        $query = $this->db->get();
       
        return $query;
    }

    function get_libur($tanggal){
        $this->db->select('*');
        $this->db->from('`master`.tanggal_libur tl');
        $this->db->where('tl.TANGGAL', $tanggal);
        $query = $this->db->get();
       
        return $query;
    }

    function getPenjamin(){
        $this->db->select('penj.ID, penj.DESKRIPSI, penpen.NORM');
        $this->db->from('pendaftaran.pendaftaran penpen');
        $this->db->join('pendaftaran.penjamin penpenj','penpen.NOMOR = penpenj.NOPEN','left');
        $this->db->join('master.referensi penj','penpenj.JENIS = penj.ID AND penj.JENIS = 10','left');
        $this->db->where('penpen.NORM', $this->input->post("norm"));
        $this->db->order_by('penpen.TANGGAL','DESC');
        $this->db->limit('1');
        $query = $this->db->get()->row();
        if ($query == null) {
            return 1;
        }else{
            return $query->ID;
        }
    }

}