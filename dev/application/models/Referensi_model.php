<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Referensi_model extends My_Model{
	protected $_table_name = 'anamnesa';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'DESC';

	function __construct(){
		parent::__construct();
	}

	public function ruangan()
	{
		if($this->input->get('q')){
			$this->db->like('mr.DESKRIPSI',$this->input->get('q'));	
		}
		$this->db->where_in('mr.ID',array('105020201','105020704','105020705','105060101','105020101','105020102','105120101','105110101','105020901','105020202','105020401','105020706','105020203','105090101','105020708','105130104','105021101','105021102','105021103','105021104','105021201','105140102','105020103','105090104','105020709'));
		$query = $this->db->get('`master`.ruangan mr');
  		
  		return $query->result();
	}

	public function smf()
	{
		if($this->input->get('q')){
			$this->db->like('mf.DESKRIPSI',$this->input->get('q'));	
		}
		$this->db->where(array('mf.JENIS' => 26, 'mf.`STATUS`' => 1));
		$query = $this->db->get('`master`.referensi mf');
  		
  		return $query->result();
	}

	public function dokter_smf()
	{
		$this->db->select('md.ID, `master`.getNamaLengkapPegawai(md.NIP) DOKTER, mp.NAMA,mp.SMF');
		$this->db->from('`master`.dokter md');
		$this->db->join('`master`.pegawai mp','md.NIP = mp.NIP','left');
		$this->db->join('`master`.referensi mr','mp.SMF = mr.ID AND mr.JENIS = 26 AND mr.STATUS = 1','left');
		$this->db->where('md.`STATUS`',1);
		$this->db->where('md.ID',$this->input->post('id'));
		// $this->db->where('mr.ID',$data);
		$this->db->group_by('md.ID');

		$query = $this->db->get();
  		
  		return $query->row();
	}

	public function dokter()
	{
		if($this->input->get('q')){
			$this->db->group_start();
			$this->db->like('mr.DESKRIPSI',$this->input->get('q'));
			$this->db->or_like('mp.NAMA',$this->input->get('q'));
			$this->db->group_end();
		}
		if($this->input->post('q')){
			$this->db->group_start();
			$this->db->like('mr.DESKRIPSI',$this->input->post('q'));
			$this->db->or_like('mp.NAMA',$this->input->post('q'));
			$this->db->group_end();
		}

		if($this->uri->segment(3) != NULL){
			$this->db->where('mp.SMF',$this->uri->segment(3));
		}

		$this->db->select('md.ID, `master`.getNamaLengkapPegawai(md.NIP) DOKTER, mp.NAMA,mp.SMF, mr.DESKRIPSI, mru.DESKRIPSI');
		$this->db->from('`master`.dokter md');
		$this->db->join('`master`.pegawai mp','md.NIP = mp.NIP','left');
		$this->db->join('`master`.referensi mr','mp.SMF = mr.ID AND mr.JENIS = 26 AND mr.STATUS = 1','left');
		$this->db->join('`master`.dokter_ruangan mdr','md.ID = mdr.DOKTER AND mdr.`STATUS` = 1 AND mdr.RUANGAN IN(105020201,105020704,105020705,105150101)','left');
		$this->db->join('`master`.ruangan mru','mdr.RUANGAN = mru.ID','left');
		$this->db->where('md.`STATUS`',1);
		// $this->db->group_start();
		// $this->db->where('mp.SMF !=',31);
		// $this->db->or_where('md.ID',10);
		// $this->db->group_end();
		// $this->db->where('mr.ID',$data);
		$this->db->group_by('md.ID');

		$query = $this->db->get();
  		
  		return $query->result();
	}

	public function rencana()
	{
		if($this->input->get('q')){
			$this->db->like('rr.DESKRIPSI',$this->input->get('q'));	
		}
		if( in_array($this->uri->segment(3), ['105020704','105020705','105020708','105020706'])){
			$this->db->where_in('rr.ID',array(1,2,3,4,5,6));
		}

		if($this->uri->segment(3) == '105120101'){
			$this->db->where_in('rr.ID',array(1,5,7,10));
		}
		if($this->uri->segment(3) == '105110101'){
			$this->db->where_in('rr.ID',array(1,8));
		}
		// if($this->uri->segment(3) == '105020201'){
		// 	$this->db->where_in('rr.ID',array(1,10));
		// }
		if($this->uri->segment(3) != '105020201'){
			$this->db->where_not_in('rr.ID',array(9));
		}

		if($this->uri->segment(3) == '105090101'){
			$this->db->where('rr.ID',11);
		}

		if($this->uri->segment(4) == 5){
			$this->db->where_in('rr.ID',array(1,2,4,5));
		}

		if(in_array($this->uri->segment(4), array(114)) && $this->uri->segment(3) != '105090101'){
			$this->db->where_in('rr.ID',array(1));
		}

		if($this->uri->segment(4) == 78){
			$this->db->where_in('rr.ID',array(1));
		}

		if($this->uri->segment(4) == 113){
			$this->db->where_not_in('rr.ID',array(2));
		}

		$query = $this->db->get('remun_medis.rencana rr');
  		
  		return $query->result();
	}

	public function total($dokter,$ruangan,$tanggal){
		$this->db->select('rp.ID_DOKTER, rp.ID_RUANGAN, COUNT(rp.ID) TOTAL');
		$this->db->from('remun_medis.perjanjian rp');
		$this->db->where(array('rp.ID_DOKTER' => $dokter, 'rp.ID_RUANGAN' => $ruangan, 'rp.TANGGAL' => $tanggal));

		$query = $this->db->get();
  		
  		return $query->result();
	}

	// Radiologi
	public function pemeriksaan()
	{
		if($this->input->get('q')){
			$this->db->like('rr.DESKRIPSI',$this->input->get('q'));	
		}
		$this->db->where('rr.STATUS',1);
		$query = $this->db->get('remun_medis.pemeriksaan_radiologi rr');
  		
  	return $query->result();
	}

	public function tindakanRadPerKelas()
	{
		if($this->input->get('q')){
			$this->db->like('tk.NAMA',$this->input->get('q'));
		}

		$this->db->select('tk.ID ,tk.NAMA');
		$this->db->from('master.tindakan tk');
		$this->db->join('master.tindakan_ruangan tr','tr.TINDAKAN=tk.ID','left');
		$this->db->where(array('tr.STATUS'=> 1,'tk.STATUS'=> 1, 'tr.RUANGAN' => 105100101));
		$this->db->group_by('tk.NAMA');

		$query = $this->db->get();
  		
  	return $query->result();
	}

	public function tujuanPemeriksaan()
	{
		if($this->input->get('q')){
			$this->db->like('rt.DESKRIPSI',$this->input->get('q'));	
		}

		$query = $this->db->get('remun_medis.tujuan_pemeriksaan_radiologi rt');
  		
  	return $query->result();
	}

	//  Prosedur Diagnosis
	public function kamarProsedur()
	{
		if($this->input->get('q')){
			$this->db->like('rp.DESKRIPSI',$this->input->get('q'));	
		}

		$query = $this->db->get('remun_medis.kamar_prosedur rp');
  		
  	return $query->result();
	}

	public function tindakanProsedur()
	{
		if($this->input->get('q')){
			$this->db->like('tk.NAMA',$this->input->get('q'));
		}

		$this->db->select('tk.ID ,tk.NAMA');
		$this->db->from('master.tindakan tk');
		$this->db->join('master.tindakan_ruangan tr','tr.TINDAKAN=tk.ID','left');
		$this->db->where(array('tr.STATUS'=> 1,'tk.STATUS'=> 1, 'tr.RUANGAN' => 105060101));
		$this->db->group_by('tk.NAMA');
		$query = $this->db->get();

  	return $query->result();
	}

	public function ruanganPelayanan()
	{
		if($this->input->get('q')){
			$this->db->like('r.DESKRIPSI',$this->input->get('q'));	
		}
		$this->db->select('r.ID, r.DESKRIPSI');
		$this->db->from('`master`.ruangan r');
		$this->db->where(array('r.JENIS'=> 5,'r.`STATUS`'=> 1));
		$this->db->where_not_in('r.JENIS_KUNJUNGAN',array(11,4));
		$query = $this->db->get();

		return $query->result();
	}

	function kamar($tanggal){
		$this->db->select('k.`NO`
		, (SELECT COUNT(pp.ID)
				FROM remun_medis.perjanjian_prosedur pp
				LEFT JOIN remun_medis.jadwal_prosedur jp ON pp.ID_JADWAL = jp.ID
				WHERE jp.TANGGAL = "'.$tanggal.'" 
				AND pp.`STATUS`!=0 AND jp.`STATUS`!=0 AND pp.KAMAR = k.NO) TOTAL');
		$this->db->from('remun_medis.kamar k ');
		$query = $this->db->get();

		return $query->result();
	}

	function kamarProsedurKuota(){
		$tanggal = '';
		if($this->uri->segment(3)){
			$tanggal = $this->uri->segment(3);
		}
		$this->db->select('kp.`*`
			, (SELECT COUNT(pp.ID) FROM remun_medis.perjanjian_prosedur pp 
					LEFT JOIN remun_medis.jadwal_prosedur jpr ON jpr.ID = pp.ID_JADWAL
				WHERE jpr.TANGGAL="'.$tanggal.'" AND jpr.ID_KAMAR=kp.ID AND pp.STATUS!=0) JML_PASIEN
			, IF((SELECT jp.KUOTA FROM remun_medis.jadwal_prosedur jp WHERE jp.ID_KAMAR=kp.ID AND jp.TANGGAL="'.$tanggal.'" limit 1) IS NULL,0
				,(SELECT jp.KUOTA FROM remun_medis.jadwal_prosedur jp WHERE jp.ID_KAMAR=kp.ID AND jp.TANGGAL="'.$tanggal.'" limit 1)) KUOTA');
		$this->db->from('remun_medis.kamar_prosedur kp');
		$this->db->where(array('kp.STATUS'=> 1));

		$query = $this->db->get();

		return $query->result();
	}
	
}
