<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User_model extends My_Model{

	function __construct(){
		parent::__construct();
	}

	function table_query()
    {
        $search=$this->input->post('search[value]');

        $this->db->select(' ap.ID, ap.NAMA,(SELECT GROUP_CONCAT("• ", m.LABEL SEPARATOR "<br>")
        FROM remun_medis.menu_user mu 
        LEFT JOIN remun_medis.menu m ON mu.ID_MENU=m.ID
        WHERE mu.ID_USER=ap.ID AND mu.`STATUS` != 0) MENU
        ,(SELECT GROUP_CONCAT("• ", f.DESKRIPSI SEPARATOR "<br>")
        FROM remun_medis.fitur_user fu 
        LEFT JOIN remun_medis.fitur f ON fu.ID_FITUR = f.ID
        WHERE fu.ID_USER=ap.ID AND fu.`STATUS` != 0) FITUR');
        $this->db->from('aplikasi.pengguna ap');
        $this->db->where('ap.`STATUS` !=',0);
        $this->db->group_by('ap.ID');

        if($this->input->post('search[value]')){
            $this->db->having("NAMA LIKE '%$search%'");       
            $this->db->or_having("MENU LIKE '%$search%'");       
            $this->db->or_having("FITUR LIKE '%$search%'");
        }

        // $this->db->from('`master`.pegawai mp ');
        // $this->db->join('master`.staff ms','mp.NIP = ms.NIP','LEFT');
        // $this->db->join('`master`.staff_ruangan msr','ms.ID = msr.ID','LEFT');
        // $this->db->join('`master`.ruangan mr','msr.RUANGAN  = mr.ID','LEFT');
        // $this->db->join('aplikasi.pengguna ap','mp.NIP = ap.NIP','LEFT');
        // $this->db->where('msr.`STATUS`',1)->where('mr.ID','105080101');
       
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

}
