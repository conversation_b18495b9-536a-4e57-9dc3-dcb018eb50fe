<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Perjanjian_model extends My_Model{
	protected $_table_name = 'remun_medis.perjanjian_radiologi';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'ASC';

    public $rules = array(
        'norm' => array(
            'field' => 'norm',
            'rules' => 'trim|required'
        ),
        'nama' => array(
            'field' => 'nama',
            'rules' => 'trim|required'
        ),
        'tgl' => array(
            'field' => 'rgl',
            'rules' => 'trim|required'
        ),
    );

    function __construct(){
        parent::__construct();
        // $this->db2 = $this->load->database('dummy',TRUE);
    }

    function table_query()
    {
        $this->db->select('p.ID, p.NOMR, p.NAMAPASIEN, p.NOMOR, j.TANGGAL, pr.DESKRIPSI
        ,(SELECT GROUP_CONCAT(CONCAT("&bull; ",mt.NAMA) SEPARATOR "<br>") 
        FROM remun_medis.pemeriksaan_tindakan t
        LEFT JOIN `master`.tindakan mt ON t.ID_TINDAKAN = mt.ID
        WHERE t.ID_PERJANJIAN_RADIOLOGI = p.ID) TINDAKAN, tpr.DESKRIPSI TUJUAN');
        $this->db->from('remun_medis.perjanjian_radiologi p');
        $this->db->join('remun_medis.jadwal_radiologi j','p.ID_JADWAL = j.ID');
        $this->db->join('remun_medis.pemeriksaan_radiologi pr','j.ID_PEMERIKSAAN = pr.ID');
        $this->db->join('remun_medis.tujuan_pemeriksaan_radiologi tpr','p.TUJUAN_PEMERIKSAAN = tpr.ID','LEFT');
        $this->db->where('p.STATUS !=', 0);

        $this->db->order_by('j.ID','DESC');
        if($this->input->post('pemeriksaan'))
        {
            $this->db->where('j.ID_PEMERIKSAAN', $this->input->post('pemeriksaan'));
        }
        if($this->input->post('awal'))
        {
            $this->db->where('j.TANGGAL', $this->input->post('awal'));
        }else{
            $this->db->where('j.TANGGAL >= CURDATE()');
        }
    }

    function create()
    {
        $post = $this->input->post();
        $this->db->trans_begin();
        $field = array(
            'NOMR' => $this->input->post('norm'),
            'NAMAPASIEN' => $this->input->post('nama'),
            'ID_JADWAL' => $this->input->post('idj'),
            'TUJUAN_PEMERIKSAAN' => $this->input->post('tujuan'),
            // 'ID_TINDAKAN' => $this->input->post('tindakan'),
            'NOMOR' => $this->input->post('nomor'),
            'OLEH' => $this->session->userdata('id'),
            'INPUT' => date('Y-m-d H:i:s'),
        );
        $this->db->insert('remun_medis.perjanjian_radiologi', $field);
        $insert_id = $this->db->insert_id();

        $id = $this->getId();
        if (isset($post['tindakan'])) {
            $data = array();
            $index = 0;
            foreach ($post['tindakan'] as $input) {
                if ($post['tindakan'][$index] != "" ) {
                    array_push(
                        $data, array(
                            'ID_PERJANJIAN_RADIOLOGI' => $insert_id,
                            'ID_TINDAKAN' => $post['tindakan'][$index],
                        )
                    );
                }
                $index++;
            }
            $this->db->insert_batch('remun_medis.pemeriksaan_tindakan', $data);
        }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 200, 'message' => 'Berhasil.', 'id' => $id);
        }

        return $result;
    }

    function batalPasien(){
        $field = array(
            'STATUS' => '0',
            'DELETED_AT' => date('Y-m-d H:i:s'),
            'DELETED_BY' => $this->session->userdata('id'),
        );
        $this->db->where('rp.ID',$this->input->post('id'));
        $this->db->update('remun_medis.perjanjian_radiologi rp', $field);

        if($this->db->affected_rows() > 0){
            return array('status' => 200, 'message' => 'Berhasil.');
        }

    }

    function cek(){
        $this->db->select('p.ID, CONCAT("Perjanjian ",pem.DESKRIPSI) INFO');
        $this->db->from('remun_medis.perjanjian_radiologi p');
        $this->db->join('remun_medis.jadwal_radiologi j','p.ID_JADWAL = j.ID');
        $this->db->join('remun_medis.pemeriksaan_radiologi pem','j.ID_PEMERIKSAAN = pem.ID');
        if($this->input->post('norm') != '0'){
            $this->db->where(array('p.NOMR' => $this->input->post('norm'), 'p.ID_JADWAL' => $this->input->post('idj'),'p.STATUS !=' => 0));
        }else{
            $this->db->where(array('p.NOMR' => $this->input->post('norm'), 'p.ID_JADWAL' => $this->input->post('idj'),'p.NOMOR' => $this->input->post('nomor'),'rp.STATUS !=' => 0));
        }
        $query = $this->db->get();
        $row = $query->row();
        $num = $query->num_rows();

        $cekKuota = $this->cekKuota();
            
        if($num > 0){
            return array('status' => 503, 'message' => 'Data Sudah Ada', 'id' => $row -> ID,'info' => $row -> INFO);
        }elseif($cekKuota -> STATUS == 0){
            return array('status' => 406, 'message' => 'Kuota Penuh');
        }
        return array('status' => 200, 'message' => 'Success');
    }

    function cekKuota(){
        $this->db->select('IF(COUNT(*) >= (SELECT j.KUOTA FROM remun_medis.jadwal_radiologi j WHERE j.ID = '.$this->input->post('idj').'), 0, 1) STATUS');
        $this->db->from('remun_medis.perjanjian_radiologi p');
        $this->db->where(array('p.ID_JADWAL' => $this->input->post('idj'),'p.STATUS !=' => 0));
        $query = $this->db->get();

        return $query->row();
    }

    function getId(){
        $this->db->select('rp.ID');
        $this->db->from('remun_medis.perjanjian_radiologi rp');
        $this->db->where(array('rp.NOMR' => $this->input->post('norm'),'rp.ID_JADWAL' => $this->input->post('idj'),'rp.STATUS !=' => 0));
        $query = $this->db->get()->row();
        $id = $query -> ID;

        return $id;
    }

    public function historyPasien()
	{
		$this->db->select('r.ID, r.NOMR, r.NAMAPASIEN, j.TANGGAL, p.DESKRIPSI PEMERIKSAAN
        , tp.DESKRIPSI TUJUAN, GROUP_CONCAT(tm.NAMA) TINDAKAN, olh.NAMA CREATED_BY
        , r.INPUT CREATED_AT, rm.NAMA DELETED_BY, r.DELETED_AT, r.STATUS');
        $this->db->from('remun_medis.perjanjian_radiologi r');
        $this->db->join('remun_medis.jadwal_radiologi j','r.ID_JADWAL=j.ID','LEFT');
        $this->db->join('remun_medis.pemeriksaan_radiologi p','j.ID_PEMERIKSAAN=p.ID','LEFT');
        $this->db->join('remun_medis.pemeriksaan_tindakan t','r.ID=t.ID_PERJANJIAN_RADIOLOGI','LEFT');
        $this->db->join('`master`.tindakan tm','t.ID_TINDAKAN=tm.ID','LEFT');
        $this->db->join('remun_medis.tujuan_pemeriksaan_radiologi tp','r.TUJUAN_PEMERIKSAAN=tp.ID','LEFT');
        $this->db->join('aplikasi.pengguna olh','r.OLEH=olh.ID','LEFT');
        $this->db->join('aplikasi.pengguna rm','r.DELETED_BY=rm.ID','LEFT');
 
		$this->db->where('r.NOMR', $this->input->post('norm'));
        $this->db->where('j.TANGGAL >= CURDATE()');
        $this->db->where('j.STATUS != 0');

        $this->db->order_by('j.TANGGAL DESC');
        $this->db->group_by(array('r.ID'));

        $query = $this->db->get();
  		
  		return $query->result();
	}
}