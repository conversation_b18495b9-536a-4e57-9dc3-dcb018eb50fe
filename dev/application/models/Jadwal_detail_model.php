<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jadwal_detail_model extends My_Model{
	protected $_table_name = 'remun_medis.jadwal_detail';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'ASC';

	function __construct(){
		parent::__construct();
	}

	/**
	 * Mengecek apakah slot sudah terisi oleh perjanjian
	 */
	function cekSlotTerisi($id_slot) {
		// Ambil data slot
		$slot = $this->get($id_slot);
		if (!$slot) {
			return array('status' => false, 'message' => 'Slot tidak ditemukan');
		}

		// Cek di tabel perjanjian berdasarkan field SLOT yang berelasi dengan jadwal_detail.ID
		$this->db->select('COUNT(*) as jumlah, GROUP_CONCAT(p.NAMAPASIEN) as nama_pasien');
		$this->db->from('remun_medis.perjanjian p');
		$this->db->where('p.SLOT', $id_slot);
		$this->db->where('p.STATUS !=', 0);

		$result = $this->db->get()->row();

		if ($result->jumlah > 0) {
			return array(
				'status' => true,
				'jumlah' => $result->jumlah,
				'nama_pasien' => $result->nama_pasien,
				'message' => "Slot sudah terisi oleh {$result->jumlah} perjanjian"
			);
		}

		return array('status' => false, 'jumlah' => 0, 'message' => 'Slot kosong');
	}

	/**
	 * Menghitung jumlah slot dalam satu jadwal
	 */
	function hitungJumlahSlot($id_jadwal) {
		$this->db->select('COUNT(*) as jumlah');
		$this->db->from($this->_table_name);
		$this->db->where('ID_JADWAL', $id_jadwal);

		$result = $this->db->get()->row();
		return $result->jumlah;
	}

	/**
	 * Mengambil slot lain dalam jadwal yang sama (untuk transfer)
	 */
	function getSlotLain($id_jadwal, $id_slot_exclude = null) {
		$this->db->select('*');
		$this->db->from($this->_table_name);
		$this->db->where('ID_JADWAL', $id_jadwal);

		if ($id_slot_exclude) {
			$this->db->where('ID !=', $id_slot_exclude);
		}

		$this->db->order_by('AWAL', 'ASC');

		return $this->db->get()->result();
	}

	/**
	 * Transfer perjanjian dari satu slot ke slot lain
	 */
	function transferPerjanjian($id_slot_asal, $id_slot_tujuan) {
		// Ambil data slot asal dan tujuan
		$slot_asal = $this->get($id_slot_asal);
		$slot_tujuan = $this->get($id_slot_tujuan);

		if (!$slot_asal || !$slot_tujuan) {
			return array('status' => false, 'message' => 'Slot tidak ditemukan');
		}

		// Cek apakah slot tujuan masih ada kapasitas
		$cek_tujuan = $this->cekSlotTerisi($id_slot_tujuan);
		if ($cek_tujuan['jumlah'] >= $slot_tujuan->KUOTA) {
			return array('status' => false, 'message' => 'Slot tujuan sudah penuh');
		}

		// Cek perjanjian yang akan dipindahkan
		$cek_asal = $this->cekSlotTerisi($id_slot_asal);
		if (!$cek_asal['status']) {
			return array('status' => false, 'message' => 'Tidak ada perjanjian yang perlu dipindahkan');
		}

		$this->db->trans_begin();

		// Update field SLOT di tabel perjanjian
		$this->db->set('SLOT', $id_slot_tujuan);
		$this->db->where('SLOT', $id_slot_asal);
		$this->db->where('STATUS !=', 0);
		$this->db->update('remun_medis.perjanjian');

		$affected_rows = $this->db->affected_rows();

		// Log transfer untuk audit trail
		$log_data = array(
			'id_slot_asal' => $id_slot_asal,
			'id_slot_tujuan' => $id_slot_tujuan,
			'jumlah_perjanjian' => $affected_rows,
			'tanggal_transfer' => date('Y-m-d H:i:s'),
			'oleh' => $this->session->userdata('id')
		);

		// Simpan log transfer (jika ada tabel log)
		// $this->db->insert('remun_medis.log_transfer_slot', $log_data);

		if ($this->db->trans_status() === FALSE || $affected_rows == 0) {
			$this->db->trans_rollback();
			return array('status' => false, 'message' => 'Gagal transfer perjanjian');
		} else {
			$this->db->trans_commit();
			return array(
				'status' => true,
				'message' => "Berhasil transfer {$affected_rows} perjanjian ke slot baru",
				'jumlah_transfer' => $affected_rows
			);
		}
	}

	/**
	 * Mengambil detail perjanjian yang menggunakan slot tertentu
	 */
	function getPerjanjianBySlot($id_slot) {
		$this->db->select('p.ID, p.NOMR, p.NAMAPASIEN, p.NOMOR, p.TANGGAL');
		$this->db->from('remun_medis.perjanjian p');
		$this->db->where('p.SLOT', $id_slot);
		$this->db->where('p.STATUS !=', 0);

		return $this->db->get()->result();
	}

	/**
	 * Validasi sebelum hapus slot
	 */
	function validasiHapusSlot($id_slot) {
		// Cek apakah slot ada
		$slot = $this->get($id_slot);
		if (!$slot) {
			return array('status' => false, 'message' => 'Slot tidak ditemukan');
		}

		// Cek jumlah slot dalam jadwal
		$jumlah_slot = $this->hitungJumlahSlot($slot->ID_JADWAL);
		if ($jumlah_slot <= 1) {
			return array('status' => false, 'message' => 'Tidak dapat menghapus slot. Minimal harus ada 1 slot dalam jadwal.');
		}

		// Cek apakah slot sudah terisi
		$cek_terisi = $this->cekSlotTerisi($id_slot);

		// Ambil detail perjanjian jika slot terisi
		$detail_perjanjian = array();
		if ($cek_terisi['status']) {
			$detail_perjanjian = $this->getPerjanjianBySlot($id_slot);
		}

		return array(
			'status' => true,
			'slot_terisi' => $cek_terisi['status'],
			'jumlah_perjanjian' => $cek_terisi['jumlah'],
			'detail_perjanjian' => $detail_perjanjian,
			'slot_lain' => $this->getSlotLain($slot->ID_JADWAL, $id_slot),
			'message' => 'Validasi berhasil'
		);
	}
}