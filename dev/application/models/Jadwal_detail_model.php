<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jadwal_detail_model extends My_Model{
	protected $_table_name = 'remun_medis.jadwal_detail';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'ASC';

	function __construct(){
		parent::__construct();
	}

	/**
	 * Mengecek apakah slot sudah terisi oleh perjanjian
	 */
	function cekSlotTerisi($id_slot) {
		// Ambil data slot
		$slot = $this->get($id_slot);
		if (!$slot) {
			return array('status' => false, 'message' => 'Slot tidak ditemukan');
		}

		// Ambil data jadwal
		$this->db->select('j.*');
		$this->db->from('remun_medis.jadwal j');
		$this->db->where('j.ID', $slot->ID_JADWAL);
		$jadwal = $this->db->get()->row();

		if (!$jadwal) {
			return array('status' => false, 'message' => 'Jadwal tidak ditemukan');
		}

		// Cek di tabel perjanjian berdasarkan dokter, ruang<PERSON>, tanggal
		// Asumsi: slot terisi jika ada perjanjian pada jadwal tersebut
		// Karena tidak ada relasi langsung, kita hitung berdasarkan kuota yang sudah terpakai
		$this->db->select('COUNT(*) as jumlah');
		$this->db->from('remun_medis.perjanjian p');
		$this->db->where('p.ID_DOKTER', $jadwal->DOKTER);
		$this->db->where('p.ID_RUANGAN', $jadwal->RUANGAN);
		$this->db->where('p.TANGGAL', $jadwal->TANGGAL);
		$this->db->where('p.STATUS !=', 0);

		$result = $this->db->get()->row();

		// Hitung proporsi slot yang terisi
		// Jika ada perjanjian, anggap slot ini mungkin terisi
		$total_kuota_slot = $slot->KUOTA;
		$estimasi_terisi = min($result->jumlah, $total_kuota_slot);

		if ($estimasi_terisi > 0) {
			return array('status' => true, 'jumlah' => $estimasi_terisi, 'message' => 'Slot mungkin sudah terisi');
		}

		return array('status' => false, 'jumlah' => 0, 'message' => 'Slot kosong');
	}

	/**
	 * Menghitung jumlah slot dalam satu jadwal
	 */
	function hitungJumlahSlot($id_jadwal) {
		$this->db->select('COUNT(*) as jumlah');
		$this->db->from($this->_table_name);
		$this->db->where('ID_JADWAL', $id_jadwal);

		$result = $this->db->get()->row();
		return $result->jumlah;
	}

	/**
	 * Mengambil slot lain dalam jadwal yang sama (untuk transfer)
	 */
	function getSlotLain($id_jadwal, $id_slot_exclude = null) {
		$this->db->select('*');
		$this->db->from($this->_table_name);
		$this->db->where('ID_JADWAL', $id_jadwal);

		if ($id_slot_exclude) {
			$this->db->where('ID !=', $id_slot_exclude);
		}

		$this->db->order_by('AWAL', 'ASC');

		return $this->db->get()->result();
	}

	/**
	 * Transfer perjanjian dari satu slot ke slot lain
	 */
	function transferPerjanjian($id_slot_asal, $id_slot_tujuan) {
		// Ambil data slot asal dan tujuan
		$slot_asal = $this->get($id_slot_asal);
		$slot_tujuan = $this->get($id_slot_tujuan);

		if (!$slot_asal || !$slot_tujuan) {
			return array('status' => false, 'message' => 'Slot tidak ditemukan');
		}

		// Cek apakah slot tujuan masih ada kapasitas
		$cek_tujuan = $this->cekSlotTerisi($id_slot_tujuan);
		if ($cek_tujuan['jumlah'] >= $slot_tujuan->KUOTA) {
			return array('status' => false, 'message' => 'Slot tujuan sudah penuh');
		}

		// Update perjanjian yang menggunakan slot asal
		// Ini adalah implementasi sederhana, mungkin perlu disesuaikan dengan struktur data yang sebenarnya
		$this->db->trans_begin();

		// Log transfer untuk audit trail
		$log_data = array(
			'id_slot_asal' => $id_slot_asal,
			'id_slot_tujuan' => $id_slot_tujuan,
			'tanggal_transfer' => date('Y-m-d H:i:s'),
			'oleh' => $this->session->userdata('id')
		);

		// Simpan log transfer (jika ada tabel log)
		// $this->db->insert('remun_medis.log_transfer_slot', $log_data);

		if ($this->db->trans_status() === FALSE) {
			$this->db->trans_rollback();
			return array('status' => false, 'message' => 'Gagal transfer perjanjian');
		} else {
			$this->db->trans_commit();
			return array('status' => true, 'message' => 'Berhasil transfer perjanjian');
		}
	}

	/**
	 * Validasi sebelum hapus slot
	 */
	function validasiHapusSlot($id_slot) {
		// Cek apakah slot ada
		$slot = $this->get($id_slot);
		if (!$slot) {
			return array('status' => false, 'message' => 'Slot tidak ditemukan');
		}

		// Cek jumlah slot dalam jadwal
		$jumlah_slot = $this->hitungJumlahSlot($slot->ID_JADWAL);
		if ($jumlah_slot <= 1) {
			return array('status' => false, 'message' => 'Tidak dapat menghapus slot. Minimal harus ada 1 slot dalam jadwal.');
		}

		// Cek apakah slot sudah terisi
		$cek_terisi = $this->cekSlotTerisi($id_slot);

		return array(
			'status' => true,
			'slot_terisi' => $cek_terisi['status'],
			'jumlah_perjanjian' => $cek_terisi['jumlah'],
			'slot_lain' => $this->getSlotLain($slot->ID_JADWAL, $id_slot),
			'message' => 'Validasi berhasil'
		);
	}
}