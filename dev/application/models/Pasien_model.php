<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pasien_model extends CI_Model {



	public function auth()
	{	
		$nomr = $this->input->post('norm');
		$birth = $this->input->post('tanggal');
		$this->db->select('mp.NORM,`master`.getNamaLengkap(mp.NORM) NAMA,DATE(mp.TANGGAL_LAHIR) TANGGAL_LAHIR,IF(mk.NOMOR IS NULL,0,mk.NOMOR) NOMOR');
		$this->db->from('`master`.pasien mp');
		$this->db->join('`master`.kontak_pasien mk','mp.NORM = mk.NORM','LEFT');
		$this->db->where('mp.NORM', $nomr);

		if(isset($_POST['tanggal'])) {
			$this->db->where('DATE(mp.TANGGAL_LAHIR)', $birth);
		}

		$query = $this->db->get()->row();
		if($query == "")
		{
			if($this->input->post('norm') == 0){
				$data = array('nomr' => '0','nama' => '','nomor' => '');
				return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
			}else{
				return array('status' => 204, 'message' => 'Pasien tidak di temukan.');
			}
		}else{
			//$id = $query->ID;
			//$username = $query->LOGIN;
			//$nama = $query->NAMA;
			$nomr = $query->NORM;
			$nama = $query->NAMA;
			$nomor = $query->NOMOR;
			//$tgl_lahir = $query->TANGGAL_LAHIR;
			// $tgl_lahir = $query->TANGGAL_LAHIR;
			$data = array('nomr' => $nomr,'nama' => $nama,'nomor' => $nomor);
				return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );

			// if($tgl_lahir == $birth)
			// {
			// 	$last_login = date("Y-m-d H:i:s");

			// 	$token = crypt(substr(MD5(rand()),0,7));
			// 	$expired_at = date("Y-m-d H:i:s", strtotime('+12 hours'));

			// 	$this->db->trans_start();
			// 	$this->db->where('ID',$id)->update('aplikasi',array('last_login' => $last_login));
			// 	if($this->db->trans_status() === FALSE)
			// 	{
			// 		$this->db->trans_rollback();
			// 		return array('status' => 500, 'message' => 'Internal server error.');
			// 	}else{
			// 		$this->db->trans_commit();
			// 		return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
			// 	}

			// 	$data = array('nomr' => $nomr,'nama' => $nama,'tgl_lahir' => $tgl_lahir);
			// 	return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
			// }else{
			// 	return array('status' => 204, 'message' => 'Wrong birth.');
			// }

		}
	}
}