<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login_model extends CI_Model {



	public function login()
	{	
		$username = $this->input->post('username');
		$password = $this->input->post('password');
		$this->db->select('ap.ID,ap.LOGIN,ap.PASSWORD,ap.NAMA,ap.NIP,SHA2("'.$password.'",256) PASS,GROUP_CONCAT(DISTINCT amu.ID_MENU) AKSES,am.MODULE,GROUP_CONCAT(DISTINCT am.MODULE) MODULE_AKSES
		, (SELECT m.LINK FROM remun_medis.menu m WHERE m.MODULE = am.MODULE ORDER BY m.SEQ LIMIT 1) LINK,GROUP_CONCAT(DISTINCT fu.ID_FITUR) FITUR');
		$this->db->from('aplikasi.pengguna ap');
		$this->db->join('remun_medis.menu_user amu','ap.ID = amu.ID_USER AND amu.STATUS != 0');
		$this->db->join('remun_medis.menu am','amu.ID_MENU = am.ID');
		$this->db->join('remun_medis.fitur_user fu','ap.ID = fu.ID_USER AND fu.`STATUS` != 0','LEFT');

		$this->db->where('LOGIN',$username);
		$this->db->group_by('amu.ID_USER');

		$query = $this->db->get()->row();
		if($query == "")
		{
			return array('status' => 204, 'message' => 'Username not found.');
		}else{
			$private_key = 'KDFLDMSTHBWWSGCBH';
			$hashed_password = $query->PASSWORD;
			$id = $query->ID;
			$username = $query->LOGIN;
			$nama = $query->NAMA;
			$akses = $query->AKSES;
			$fitur = $query->FITUR;
			$module = $query->MODULE;
			$module_akses = $query->MODULE_AKSES;
			$link = $query->LINK;

			$passwordMD5 = MD5($private_key.MD5($password).$private_key);

			if(hash_equals($hashed_password,$passwordMD5) || $query->PASSWORD == $query->PASS)
			{
				// $last_login = date("Y-m-d H:i:s");

				// $token = crypt(substr(MD5(rand()),0,7));
				// $expired_at = date("Y-m-d H:i:s", strtotime('+12 hours'));

				// $this->db->trans_start();
				// $this->db->where('ID',$id)->update('aplikasi',array('last_login' => $last_login));
				// if($this->db->trans_status() === FALSE)
				// {
				// 	$this->db->trans_rollback();
				// 	return array('status' => 500, 'message' => 'Internal server error.');
				// }else{
				// 	$this->db->trans_commit();
				// 	return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
				// }

				$data = array('id' => $id , 'username' => $username, 'nama' => $nama, 'akses' => $akses, 'fitur' => $fitur, 'module' => $module, 'module_akses' => $module_akses, 'link' => $link);
				return array('status' => 200, 'message' => 'Successfully login.', 'data' => $data );
			}else{
				return array('status' => 204, 'message' => 'Wrong password.');
			}

		}
	}
}