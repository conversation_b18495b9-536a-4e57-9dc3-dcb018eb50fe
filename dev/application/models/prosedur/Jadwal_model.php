<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jadwal_model extends My_Model{
	protected $_table_name = 'remun_medis.jadwal_prosedur';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
	protected $_order_by_type = 'ASC';

	public $rules = array(
			'jkamar' => array(
				'field' => 'jkamar',
				'rules' => 'trim|required'
			),
			'jtanggal' => array(
				'field' => 'jtanggal',
				'rules' => 'trim|required'
			),
			'kuota' => array(
				'field' => 'kuota',
				'rules' => 'trim|required'
			),
    );
    
    public $rules_multiple = array(
        'jkamar[]' => array(
            'field' => 'jkamar[]',
            'rules' => 'trim|required'
        ),
        // 'jtanggal[]' => array(
        //     'field' => 'jtanggal[]',
        //     'rules' => 'trim|required'
        // ),
        'kuota' => array(
            'field' => 'kuota',
            'rules' => 'trim|required'
        ),
    );

	function __construct(){
		parent::__construct();
	}

    public function jadwal()
	{
		$this->db->select('j.ID,j.TANGGAL, j.KU<PERSON>, DATE_FORMAT(j.TANGGAL, "%d") DAY,DAYNAME(j.TANGGAL) HARI, SUM(CASE WHEN p.status != 0 THEN 1 ELSE 0 END) AS JUMLAH');
        $this->db->from('remun_medis.jadwal_prosedur j');
        $this->db->join('remun_medis.perjanjian_prosedur p','j.ID = p.ID_JADWAL','LEFT');
 
        $this->db->where('j.TANGGAL >= CURDATE()');
        $this->db->where('j.STATUS != 0');

        $this->db->order_by('j.TANGGAL ASC');
        $this->db->group_by(array('j.ID'));

        if($this->input->post('kamar')){
        	$this->db->where('j.ID_KAMAR',$this->input->post('kamar'));
        }

        if($this->input->post('tanggal')){
        	$this->db->where('j.TANGGAL',$this->input->post('tanggal'));
        }else{
		    $this->db->where('DATE_FORMAT(j.TANGGAL, "%Y-%m") ="'. $this->input->post('bulan').'"');
        }

        $query = $this->db->get();
  		
  		return $query->result();
	}

	function cek(){
        $this->db->select('j.ID');
        $this->db->from('remun_medis.jadwal_prosedur j');
        $this->db->where(array('j.ID_KAMAR' => $this->input->post('jkamar'), 'j.TANGGAL' => $this->input->post('jtanggal'), 'j.STATUS !=' => 0));
        $query = $this->db->get();
        $row = $query->row();
        $num = $query->num_rows();

        if($num > 0){
            return array('status' => 503, 'message' => 'Data Sudah Ada', 'id' => $row -> ID);
        }
        return array('status' => 200, 'message' => 'Success');

    }

    function get_table($single = TRUE){
        $this->db->select('rj.ID,rp.ID ID_KAMAR,rp.DESKRIPSI,rj.TANGGAL,rj.KUOTA');
        $this->db->from('remun_medis.jadwal_prosedur rj');
        $this->db->join('remun_medis.kamar_prosedur rp','rj.ID_KAMAR = rp.ID');
        $this->db->where('rj.ID',$this->input->post('id'));

        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
	}
	
	function calendar($single = TRUE){
        $start = $this->input->get('start');
        $end = $this->input->get('end');
        $this->db->select('j.ID, p.DESKRIPSI, j.TANGGAL START, j.TANGGAL END, j.KUOTA');
        $this->db->from('remun_medis.jadwal_prosedur j');
        $this->db->join('remun_medis.kamar_prosedur p','j.ID_KAMAR = p.ID');
        // $this->db->where('j.DOKTER', $this->uri->segment(3));
        $this->db->where("j.TANGGAL BETWEEN '$start' AND '$end'");
        $this->db->where('j.STATUS !=', 0);
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

    function get_jadwal($kamar,$tanggal){
        $this->db->select('*');
        $this->db->from('remun_medis.jadwal_prosedur j');
        $this->db->where('j.ID_KAMAR', $kamar);
        $this->db->where('j.TANGGAL', $tanggal);
        $this->db->where('j.STATUS !=', 0);
        $query = $this->db->get();
       
        return $query;
    }

    function get_libur($tanggal){
        $this->db->select('*');
        $this->db->from('`master`.tanggal_libur tl');
        $this->db->where('tl.TANGGAL', $tanggal);
        $query = $this->db->get();
       
        return $query;
    }

}