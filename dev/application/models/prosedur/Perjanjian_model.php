<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Perjanjian_model extends My_Model{
	protected $_table_name = 'remun_medis.perjanjian_prosedur';
    protected $_primary_key = 'ID';
    protected $_order_by = 'ID';
    protected $_order_by_type = 'ASC';

    public $rules = array(
        'norm' => array(
            'field' => 'norm',
            'rules' => 'trim|required'
        ),
        'nama' => array(
            'field' => 'nama',
            'rules' => 'trim|required'
        ),
        'tgl' => array(
            'field' => 'rgl',
            'rules' => 'trim|required'
        ),
    );

    function __construct(){
        parent::__construct();
        // $this->db2 = $this->load->database('dummy',TRUE);
    }

    function table_query()
    {
        $this->db->select('p.ID, p.NOMR, p.NAMAPASIEN, p.NOMOR, j.TANGGAL, pr.DESKRIPSI
        ,(SELECT GROUP_CONCAT(CONCAT("&bull; ",mt.NAMA) SEPARATOR "<br>") 
        FROM remun_medis.pemeriksaan_tindakan_prosedur t
        LEFT JOIN `master`.tindakan mt ON t.ID_TINDAKAN = mt.ID
        WHERE t.ID_PERJANJIAN_PROSEDUR = p.ID) TINDAKAN
        , CONCAT(p.AWAL," - ",p.AKHIR) JAM, r.DESKRIPSI RUANGAN, master.getNamaLengkapPegawai(d.NIP) DOKTER');
        $this->db->from('remun_medis.perjanjian_prosedur p');
        $this->db->join('remun_medis.jadwal_prosedur j','p.ID_JADWAL = j.ID');
        $this->db->join('remun_medis.kamar_prosedur pr','j.ID_KAMAR = pr.ID','LEFT');
        $this->db->join('master.ruangan r','p.ASALRUANGAN = r.ID','LEFT');
        $this->db->join('master.dokter d','p.DOKTER = d.ID','LEFT');
        $this->db->where('p.STATUS !=', 0);

        $this->db->order_by('j.ID','DESC');
        if($this->input->post('pemeriksaan'))
        {
            $this->db->where('j.ID_PEMERIKSAAN', $this->input->post('pemeriksaan'));
        }
        if($this->input->post('kamar'))
        {
            $this->db->where('j.ID_KAMAR', $this->input->post('kamar'));
        }
        if($this->input->post('awal'))
        {
            $this->db->where('j.TANGGAL', $this->input->post('awal'));
        }else{
            $this->db->where('j.TANGGAL >= CURDATE()');
        }
    }

    function create()
    {
        $post = $this->input->post();
        $this->db->trans_begin();
        $field = array(
            'NOMR' => $this->input->post('norm'),
            'NAMAPASIEN' => $this->input->post('nama'),
            'ID_JADWAL' => $this->input->post('idj'),
            'AWAL' => empty($this->input->post('awal')) ? null : $this->input->post('awal'),
            'AKHIR' => empty($this->input->post('akhir')) ? null : $this->input->post('akhir'),
            'ASALRUANGAN' => $this->input->post('ruangan'),
            'DOKTER' => $this->input->post('dokter'),
            'NOMOR' => $this->input->post('nomor'),
            'OLEH' => $this->session->userdata('id'),
        );
        $this->db->insert('remun_medis.perjanjian_prosedur', $field);
        $insert_id = $this->db->insert_id();

        $id = $this->getId();

        if (isset($post['tindakanProsedur'])) {
            $data = array();
            $index = 0;
            foreach ($post['tindakanProsedur'] as $input) {
                if ($post['tindakanProsedur'][$index] != "" ) {
                    array_push(
                        $data, array(
                            'ID_PERJANJIAN_PROSEDUR' => $insert_id,
                            'ID_TINDAKAN' => $post['tindakanProsedur'][$index],
                        )
                    );
                }
                $index++;
            }
            $this->db->insert_batch('remun_medis.pemeriksaan_tindakan_prosedur', $data);
        }

        if ($this->db->trans_status() === false) {
            $this->db->trans_rollback();
            $result = array('status' => 'failed');
        } else {
            $this->db->trans_commit();
            $result = array('status' => 200, 'message' => 'Berhasil.', 'id' => $id);
        }

        return $result;
    }

    function batalPasien(){
        $field = array(
            'STATUS' => '0',
            'DELETED_AT' => date('Y-m-d h:i:s'),
            'DELETED_BY' => $this->session->userdata('id'),
        );
        $this->db->where('rp.ID',$this->input->post('id'));
        $this->db->update('remun_medis.perjanjian_prosedur rp', $field);

        if($this->db->affected_rows() > 0){
            return array('status' => 200, 'message' => 'Berhasil.');
        }

    }

    function cek(){
        $this->db->select('p.ID, CONCAT("Perjanjian ",pem.DESKRIPSI) INFO');
        $this->db->from('remun_medis.perjanjian_prosedur p');
        $this->db->join('remun_medis.jadwal_prosedur j','p.ID_JADWAL = j.ID');
        $this->db->join('remun_medis.kamar_prosedur pem','j.ID_KAMAR = pem.ID');
        if($this->input->post('norm') != '0'){
            $this->db->where(array('p.NOMR' => $this->input->post('norm'), 'p.ID_JADWAL' => $this->input->post('idj'),'p.STATUS !=' => 0));
        }else{
            $this->db->where(array('p.NOMR' => $this->input->post('norm'), 'p.ID_JADWAL' => $this->input->post('idj'),'p.NOMOR' => $this->input->post('nomor'),'rp.STATUS !=' => 0));
        }
        $query = $this->db->get();
        $row = $query->row();
        $num = $query->num_rows();

        $cekKuota = $this->cekKuota();
            
        if($num > 0){
            return array('status' => 503, 'message' => 'Data Sudah Ada', 'id' => $row -> ID,'info' => $row -> INFO);
        }elseif($cekKuota -> STATUS == 0){
            return array('status' => 406, 'message' => 'Kuota Penuh');
        }
        return array('status' => 200, 'message' => 'Success');
    }

    function cekKuota(){
        $this->db->select('IF(COUNT(*) >= (SELECT j.KUOTA FROM remun_medis.jadwal_prosedur j WHERE j.ID = '.$this->input->post('idj').'), 0, 1) STATUS');
        $this->db->from('remun_medis.perjanjian_prosedur p');
        $this->db->where(array('p.ID_JADWAL' => $this->input->post('idj'),'p.STATUS !=' => 0));
        $query = $this->db->get();

        return $query->row();
    }

    function getId(){
        $this->db->select('rp.ID');
        $this->db->from('remun_medis.perjanjian_prosedur rp');
        $this->db->where(array('rp.NOMR' => $this->input->post('norm'),'rp.ID_JADWAL' => $this->input->post('idj'),'rp.STATUS !=' => 0));
        $query = $this->db->get()->row();
        $id = $query -> ID;

        return $id;
    }

    public function pasienKamar()
	{
		$this->db->select('pp.NAMAPASIEN, CONCAT(pp.AWAL,"-",pp.AKHIR) JAM');
		$this->db->from('remun_medis.perjanjian_prosedur pp');
		$this->db->join('remun_medis.jadwal_prosedur jp','pp.ID_JADWAL = jp.ID','left');
		$this->db->where(array('pp.STATUS !='=> 0,'jp.STATUS !='=> 0, 'pp.KAMAR' => $this->input->post('kamar'), 'jp.TANGGAL' => $this->input->post('tanggal')));
		$query = $this->db->get();

  	    return $query->result();
	}

    function cekDokter(){
        $this->db->select('COUNT(*) STATUS');
        $this->db->from('remun_medis.jadwal j');
        $this->db->where(array('j.DOKTER' => $this->input->post('dokter'),'j.TANGGAL' => $this->input->post('tanggal'), 'j.RUANGAN !=' => '105060101','j.STATUS !=' => 0));
        $query = $this->db->get();

        return $query->row();
    }
    
}