<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Fitur_user_model extends My_Model{

	protected $_table_name = 'remun_medis.fitur_user';
	protected $_primary_key = 'ID';
	protected $_order_by = 'ID';
    protected $_order_by_type = 'DESC';
    
    public $rules = array(
		'fitur[]' => array(
            'field' => 'fitur[]',
            'label' => 'Fitur',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Ang<PERSON>.'
                ),
        ),

        'id' => array(
            'field' => 'id',
            'label' => 'User',
            'rules' => 'trim|numeric|required',
            'errors' => array(
                        'required' => '%s Wajib Diisi.',
                        'numeric' => '%s Wajib Angka.'
                ),
        ),
	);

	function table_query()
    {
        $this->db->select('amu.ID, am.LABEL');
		$this->db->from('remun_medis.fitur am');
		$this->db->join('remun_medis.fitur_user amu','am.ID = amu.ID_FITUR');
        $this->db->where('amu.ID_USER',$this->input->post('id'))->where('amu.STATUS !=',0);
        
        if($this->input->post('idm')){
            $this->db->where('amu.ID_FITUR',$this->input->post('idm'));
        }
		
    }

    function get_table($single = TRUE){
        $this->table_query();
        $query = $this->db->get();
        if($single == TRUE){
            $method = 'row';
        }

        else{
            $method = 'result';
        }
        return $query->$method();
    }

}
