<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Sms{

	public function send($nomor,$pesan){

		$url = "http://**************/masking/send_post.php"; 
		$rows = array ( 
			'username' => 'rskdharmais_sms', 
			'password' => 'simrsipde2019', 
			'hp' => $nomor, 
			'message' => $pesan 
		); 
		$curl = curl_init(); 
		curl_setopt( $curl, CURLOPT_URL, $url ); 
		curl_setopt( $curl, CURLOPT_POST, TRUE ); 
		curl_setopt( $curl, CURLOPT_RETURNTRANSFER, TRUE ); 
		curl_setopt( $curl, CURLOPT_POSTFIELDS, 
		http_build_query($rows) ); 
		curl_setopt( $curl, CURLOPT_HEADER, FALSE ); 
		curl_setopt($curl, CURLOPT_CONNECTTIMEOUT, 60); 
		curl_setopt($curl, CURLOPT_TIMEOUT, 60); 
		$htm = curl_exec($curl); 
	
		return $htm ;
	}


}