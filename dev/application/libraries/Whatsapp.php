<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Whatsapp{
    
    public function send($number,$message){
        require 'vendor/autoload.php';

		$token = 'd3C5H4RudHj6ZdRO1gGqNxRLmbmnvHaGVy02badpxA9l3+TED6NIiqEcLBePSz83zoWWeZwzUNGnw62d1kj4Nw==';

        $client = new \GuzzleHttp\Client(['base_uri' => 'https://app.lenna.ai/app/public/api/', 'verify' => false]);

        $response = $client->request(
            'POST', 
            'PdR6qd/send-hsm',
            [
                'json' => [
                    "integrationId" => 174,
                    "token" => $token,
                    "channel" => "whatsapp",
                    "type" => "broadcast",
                    "category" => "hsm",
                    "templateName" => "notifikasi_appointment_pasien",
                    "messageTitle" => "title",
                    "phone" => [$number],
                    "templateParams" => $message
                ]
            ]
        );
        
        $data = $response->getBody();
        $data = json_decode($data, true);
        return $data;
	}


}