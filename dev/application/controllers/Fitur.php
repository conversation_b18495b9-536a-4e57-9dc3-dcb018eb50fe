<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Fitur extends Backend_Controller{


    function __construct() {
        parent::__construct();
        $this->load->model(array('Fitur_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function fitur()
    {
        $result = $this->Fitur_model->get_table(FALSE);
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['idm'] = $row -> IDM;
            $sub_array['text'] = $row -> LABEL;
            $sub_array['status'] = $row -> STATUS;

            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

}

