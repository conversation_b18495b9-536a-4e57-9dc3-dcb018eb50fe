<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Info_pasien extends Backend_Controller{


    function __construct() {
        parent::__construct();
        $this->load->model(array('Info_pasien_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function datatables(){
        $result = $this->Info_pasien_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $row -> PESAN;
            $sub_array[] = $row -> WAKTU_INPUT;
            $sub_array[] = '<button type="button" class="btn btn-primary btn-sm detilInfo" data-id="'.$row -> ID.'">Lihat</button>';

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->Info_pasien_model->total_count(),
            "recordsFiltered"   => $this->Info_pasien_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }
}

