<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jadwal extends Backend_Controller{

    function __construct() {
        parent::__construct();
        $this->load->model(array('Jadwal_model','Jadwal_detail_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function index(){
        if(!in_array(3,$this->session->userdata('akses'))){
            $this->site->view('error_403');
        }else{
            $this->site->view('jadwal/index');
        }

    }

    public function view(){
        if(!in_array(3,$this->session->userdata('akses'))){
            $this->site->view('error_403');
        }else{
            $this->site->view('jadwal/calendar');
        }
    }

    public function edit(){
        if(!in_array(3,$this->session->userdata('akses'))){
            $this->site->view('error_403');
        }else{
            $this->site->view('jadwal/edit');
        }
    }

    public function datatables(){
        $result = $this->Jadwal_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            // $sub_array[] = $row -> JENIS;
            $sub_array[] = $row -> ID;
            $sub_array[] = $row -> DESKRIPSI;
            $sub_array[] = $row -> STATUS == 1 ? '<i class="far fa-check-square fa-2x hapus" id="'.$row -> ID.'" jenis="'.$row -> JENIS.'"></i>' : '<i class="far fa-square fa-2x hapus" id="'.$row -> ID.'" jenis="'.$row -> JENIS.'"></i>';

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->Jadwal_model->total_count(),
            "recordsFiltered"   => $this->Jadwal_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }

    public function jadwal(){
        $result = $this->Jadwal_model->jadwal();
        $data = array();
        $hari = array(
            'Monday' => 'Senin',
            'Tuesday' => 'Selasa',
            'Wednesday' => 'Rabu',
            'Thursday' => 'Kamis',
            'Friday' => 'Jumat',
            'Saturday' => 'Sabtu',
            'Sunday' => 'Minggu',
        );
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['tanggal'] = $row -> TANGGAL;
            $sub_array['day'] = $row -> DAY;
            $sub_array['waktu'] = $row -> WAKTU;
            $sub_array['jumlah'] = $row -> JUMLAH;
            $sub_array['kuota'] = $row -> KUOTA;
            $sub_array['waktu_sore'] = $row -> WAKTU_SORE;
            $sub_array['jumlah_sore'] = $row -> JUMLAH_SORE;
            $sub_array['kuota_sore'] = $row -> KUOTASORE;
            $sub_array['ruangan'] = $row -> DESKRIPSI;
            $sub_array['id_ruangan'] = $row -> ID_RUANGAN;
            $sub_array['color'] = $row -> COLOR == NULL ? '#218496' : $row -> COLOR;
            $sub_array['hari'] = $hari[$row -> HARI];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function action($param){
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                $rules = $this->Jadwal_model->rules;
                $rules_pagi = $this->Jadwal_model->rules_pagi;
                $rules_sore = $this->Jadwal_model->rules_sore;
                if($this->input->post('awal') != '' || $this->input->post('akhir') != '' || $this->input->post('kuota') != ''){
                    $rules = array_merge($rules, $rules_pagi);
                    $flag = 1;
                }
                if($this->input->post('awal_sore') != '' || $this->input->post('akhir_sore') != '' || $this->input->post('kuota_sore') != ''){
                    $rules = array_merge($rules, $rules_sore);
                    $flag = 1;
                }
                if($flag == 0){
                    $rules = array_merge($rules, $rules_pagi, $rules_sore);
                }
                
                $this->form_validation->set_rules($rules);
                $cek = $this->Jadwal_model->cek();

                if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $data = array(
                        'DOKTER' => $post['jdokter'],
                        'RUANGAN' => $post['jruangan'],
                        'TANGGAL' => $post['jtanggal'],
                        'AWAL' => !empty($post['awal']) ? $post['awal'] : NULL,
                        'AKHIR' => !empty($post['akhir']) ? $post['akhir'] : NULL,
                        'KUOTA' => $post['kuota'] != '' ? $post['kuota'] : NULL,
                        'AWAL_SORE' => !empty($post['awal_sore']) ? $post['awal_sore'] : NULL,
                        'AKHIR_SORE' => !empty($post['akhir_sore']) ? $post['akhir_sore'] : NULL,
                        'KUOTASORE' => $post['kuota_sore'] != '' ? $post['kuota_sore'] : NULL,
                        'LOCK' => !empty($post['lock_kiosk']) ? 1 : 0,
                        'OLEH' => $this->session->userdata('id'),
                    );

                    $index2 = 0;
                    if(!empty($post['jid'])){
                        $today = date("Y-m-d");
                        $one_day_before = date("Y-m-d", strtotime($post['jtanggal']."-1 day"));
                        if($post['jruangan'] == '105020201' && $today >= $one_day_before) {
                            $result = array('status' => 'failed','errors' => array('msg' => 'Data tidak bisa diubah!'));
                        } else {
                            $this->db->trans_begin();

                            $this->Jadwal_model->update($data, array('ID' => $post['jid']));
                            $id_jadwal = $post['jid'];
                            foreach ($post['id_detail'] as $input) {
                                if (!empty($post['id_detail'][$index2])) {
                                    $data_jadwal_detail = array(
                                        'ID_JADWAL' => $id_jadwal,
                                        'AWAL' => !empty($post['detail_awal'][$index2]) ? $post['detail_awal'][$index2] : NULL,
                                        'AKHIR' => !empty($post['detail_akhir'][$index2]) ? $post['detail_akhir'][$index2] : NULL,
                                        'KUOTA' => $post['detail_kuota'][$index2] != '' ? $post['detail_kuota'][$index2] : NULL,
                                    );
                                    $this->Jadwal_detail_model->update($data_jadwal_detail, array('ID' => $post['id_detail'][$index2]));
                                } else {
                                    $data_jadwal_detail = array(
                                        'ID_JADWAL' => $id_jadwal,
                                        'AWAL' => !empty($post['detail_awal'][$index2]) ? $post['detail_awal'][$index2] : NULL,
                                        'AKHIR' => !empty($post['detail_akhir'][$index2]) ? $post['detail_akhir'][$index2] : NULL,
                                        'KUOTA' => $post['detail_kuota'][$index2] != '' ? $post['detail_kuota'][$index2] : NULL,
                                    );
                                    $this->Jadwal_detail_model->insert($data_jadwal_detail);
                                }
                                $index2++;
                            }

                            if ($this->db->trans_status() === FALSE) {
                                $this->db->trans_rollback();
                                $result = array('status' => 'failed');
                            } else {
                                $this->db->trans_commit();
                                $result = array('status' => 'success');
                            }
                        }
                    }else{
                        if($cek['status'] == 200){
                            $this->db->trans_begin();

                            $id_jadwal = $this->Jadwal_model->insert($data);
                            foreach ($post['detail_awal'] as $input) {
                                if ($post['detail_awal'][$index2] != "" ) {
                                    $data_jadwal_detail = array(
                                        'ID_JADWAL' => $id_jadwal,
                                        'AWAL' => !empty($post['detail_awal'][$index2]) ? $post['detail_awal'][$index2] : NULL,
                                        'AKHIR' => !empty($post['detail_akhir'][$index2]) ? $post['detail_akhir'][$index2] : NULL,
                                        'KUOTA' => $post['detail_kuota'][$index2] != '' ? $post['detail_kuota'][$index2] : NULL,
                                    );
                                    $this->Jadwal_detail_model->insert($data_jadwal_detail);
                                }
                                $index2++;
                            }

                            if ($this->db->trans_status() === FALSE) {
                                $this->db->trans_rollback();
                                $result = array('status' => 'failed');
                            } else {
                                $this->db->trans_commit();
                                $result = array('status' => 'success');
                            }
                        }else{
                            $result = array('status' => 'failed','errors' => array('msg' => 'Data Sudah Ada!'));
                        }
                    }
                }else{
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            }else if($param == 'multiple'){
                $flag = 0;
                $rules = $this->Jadwal_model->rules_multiple;
                $rules_pagi = $this->Jadwal_model->rules_pagi;
                $rules_sore = $this->Jadwal_model->rules_sore;
                if($this->input->post('awal') != '' || $this->input->post('akhir') != '' || $this->input->post('kuota') != ''){
                    $rules = array_merge($rules, $rules_pagi);
                    $flag = 1;
                }
                if($this->input->post('awal_sore') != '' || $this->input->post('akhir_sore') != '' || $this->input->post('kuota_sore') != ''){
                    $rules = array_merge($rules, $rules_sore);
                    $flag = 1;
                }
                if($flag == 0){
                    $rules = array_merge($rules, $rules_pagi, $rules_sore);
                }
                
                $this->form_validation->set_rules($rules);

                $index = 0;
                if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    if (isset($post['jtanggal'])) {
                        foreach ($post['jtanggal'] as $input) {
                            if ($post['jtanggal'][$index] != "" ) {
                                $data_jadwal = array(
                                    'DOKTER' => $post['jdokter'][$index],
                                    'RUANGAN' => $post['jruangan'][$index],
                                    'TANGGAL' => $post['jtanggal'][$index],
                                    'AWAL' => !empty($post['awal']) ? $post['awal'] : NULL,
                                    'AKHIR' => !empty($post['akhir']) ? $post['akhir'] : NULL,
                                    'KUOTA' => $post['kuota'] != '' ? $post['kuota'] : NULL,
                                    'AWAL_SORE' => !empty($post['awal_sore']) ? $post['awal_sore'] : NULL,
                                    'AKHIR_SORE' => !empty($post['akhir_sore']) ? $post['akhir_sore'] : NULL,
                                    'KUOTASORE' => $post['kuota_sore'] != '' ? $post['kuota_sore'] : NULL,
                                    'OLEH' => $this->session->userdata('id'),
                                );

                                $index2 = 0;
                                if(!empty($post['jid'][$index])){
                                    $existing_details = $this->db->get_where('remun_medis.jadwal_detail', array(
                                        'ID_JADWAL' => $post['jid'][$index],
                                    ))->result();

                                    if (!$existing_details) {
                                        $this->Jadwal_model->update($data_jadwal, array('ID' => $post['jid'][$index]));
                                        foreach ($post['detail_awal'] as $input) {
                                            if ($post['detail_awal'][$index2] != "" ) {
                                                $data_jadwal_detail = array(
                                                    'ID_JADWAL' => $post['jid'][$index],
                                                    'AWAL' => !empty($post['detail_awal'][$index2]) ? $post['detail_awal'][$index2] : NULL,
                                                    'AKHIR' => !empty($post['detail_akhir'][$index2]) ? $post['detail_akhir'][$index2] : NULL,
                                                    'KUOTA' => $post['detail_kuota'][$index2] != '' ? $post['detail_kuota'][$index2] : NULL,
                                                );
                                                $this->Jadwal_detail_model->insert($data_jadwal_detail);
                                            }
                                            $index2++;
                                        }
                                    }
                                }else{
                                    $id_jadwal = $this->Jadwal_model->insert($data_jadwal);
                                    foreach ($post['detail_awal'] as $input) {
                                        if ($post['detail_awal'][$index2] != "" ) {
                                            $data_jadwal_detail = array(
                                                'ID_JADWAL' => $id_jadwal,
                                                'AWAL' => !empty($post['detail_awal'][$index2]) ? $post['detail_awal'][$index2] : NULL,
                                                'AKHIR' => !empty($post['detail_akhir'][$index2]) ? $post['detail_akhir'][$index2] : NULL,
                                                'KUOTA' => $post['detail_kuota'][$index2] != '' ? $post['detail_kuota'][$index2] : NULL,
                                            );
                                            $this->Jadwal_detail_model->insert($data_jadwal_detail);
                                        }
                                        $index2++;
                                    }
                                }
                            }
                            $index++;
                        }
                    }

                    if($this->db->affected_rows() >  0){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
                }else{
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            }else if($param == 'multipleUpdate'){
                $flag = 0;
                $rules = $this->Jadwal_model->rules_multiple;
                $rules_pagi = $this->Jadwal_model->rules_pagi;
                $rules_sore = $this->Jadwal_model->rules_sore;
                if($this->input->post('awal') != '' || $this->input->post('akhir') != '' || $this->input->post('kuota') != ''){
                    $rules = array_merge($rules, $rules_pagi);
                    $flag = 1;
                }
                if($this->input->post('awal_sore') != '' || $this->input->post('akhir_sore') != '' || $this->input->post('kuota_sore') != ''){
                    $rules = array_merge($rules, $rules_sore);
                    $flag = 1;
                }
                if($flag == 0){
                    $rules = array_merge($rules, $rules_pagi, $rules_sore);
                }

                $this->form_validation->set_rules($rules);
                $data = array();
                $index = 0;
                if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    if (isset($post['jtanggal'])) {
                        foreach ($post['jtanggal'] as $input) {
                            if ($post['jtanggal'][$index] != "" ) {
                                $data = array(
                                    'AWAL' => !empty($post['awal']) ? $post['awal'] : NULL,
                                    'AKHIR' => !empty($post['akhir']) ? $post['akhir'] : NULL,
                                    'KUOTA' => $post['kuota'] != '' ? $post['kuota'] : NULL,
                                    'AWAL_SORE' => !empty($post['awal_sore']) ? $post['awal_sore'] : NULL,
                                    'AKHIR_SORE' => !empty($post['akhir_sore']) ? $post['akhir_sore'] : NULL,
                                    'KUOTASORE' => $post['kuota_sore'] != '' ? $post['kuota_sore'] : NULL,
                                    'OLEH' => $this->session->userdata('id'),
                                );
                                $this->Jadwal_model->update($data, array('ID' => $post['jid'][$index]));

                            }
                            $index++;
                        }
                    }
                    if($this->db->affected_rows() >  0){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
                }else{
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            }else if($param == 'ambil'){
                $post = $this->input->post(NULL,TRUE);
                    $where = array(
                        'ID' => $post['id'],
                    );
                    echo json_encode(array(
                        'status' => 'success',
                        'data' => $this->Jadwal_model->get_table()
                    ));
            }else if($param == 'ambilDetail'){
                $post = $this->input->post(NULL,TRUE);
                $where = array(
                    'ID_JADWAL' => $post['id'],
                );
                echo json_encode(array(
                    'status' => 'success',
                    'data' => $this->Jadwal_detail_model->get_by($where),
                ));
            }else if($param == 'aktif'){
                $post = $this->input->post(NULL,TRUE);
                if(!empty($post['id'])){
                    $data = array(
                        // 'KETERANGAN' => $this->input->post('keterangan'),
                        'BATAL_OLEH' => $this->session->userdata('id'),
                        'STATUS' => '0',
                    );
                    $this->Jadwal_model->update($data, array('ID' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            }else if($param == 'hapusSlot'){
                $post = $this->input->post(NULL,TRUE);
                $id_slot = $post['id_slot'];

                if(empty($id_slot)){
                    $result = array('status' => 'failed', 'message' => 'ID slot tidak boleh kosong');
                    echo json_encode($result);
                    return;
                }

                // Validasi sebelum hapus
                $validasi = $this->Jadwal_detail_model->validasiHapusSlot($id_slot);

                if(!$validasi['status']){
                    $result = array('status' => 'failed', 'message' => $validasi['message']);
                    echo json_encode($result);
                    return;
                }

                // Jika slot sudah terisi, perlu transfer
                if($validasi['slot_terisi']){
                    $id_slot_tujuan = $post['id_slot_tujuan'] ?? null;

                    if(empty($id_slot_tujuan)){
                        // Return data untuk modal transfer
                        $result = array(
                            'status' => 'need_transfer',
                            'message' => 'Slot sudah terisi. Pilih slot tujuan untuk transfer.',
                            'jumlah_perjanjian' => $validasi['jumlah_perjanjian'],
                            'slot_lain' => $validasi['slot_lain']
                        );
                        echo json_encode($result);
                        return;
                    }

                    // Lakukan transfer
                    $transfer = $this->Jadwal_detail_model->transferPerjanjian($id_slot, $id_slot_tujuan);
                    if(!$transfer['status']){
                        $result = array('status' => 'failed', 'message' => $transfer['message']);
                        echo json_encode($result);
                        return;
                    }
                }

                // Hapus slot
                $this->Jadwal_detail_model->delete($id_slot);

                if($this->db->affected_rows() > 0){
                    $result = array('status' => 'success', 'message' => 'Slot berhasil dihapus');
                }else{
                    $result = array('status' => 'failed', 'message' => 'Gagal menghapus slot');
                }

                echo json_encode($result);
            }else if($param == 'cekSlot'){
                $post = $this->input->post(NULL,TRUE);
                $id_slot = $post['id_slot'];

                if(empty($id_slot)){
                    $result = array('status' => 'failed', 'message' => 'ID slot tidak boleh kosong');
                    echo json_encode($result);
                    return;
                }

                $validasi = $this->Jadwal_detail_model->validasiHapusSlot($id_slot);
                echo json_encode($validasi);
            }
        }
    }

    public function import(){

        $path = getcwd()."/uploads/";
        // $path = "/tmp/";

        $file_name = $_FILES['file']['name'];
        $file_type = $_FILES['file']['type'];
        $file_tmp = $_FILES['file']['tmp_name'];

        $newname = time().".xlsx" ;
        if ($file_type != 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        {
            echo json_encode('error');       
        }
        else
        {
            move_uploaded_file($file_tmp, $path.$newname);
            // chmod($path.'tess.xlsx',0755);
            // $data = $this->upload->data();
            // $data = array('upload_data' => $this->upload->data());
            // echo $data['file_name'];
            // echo json_encode($data);
        

            // $this->load->library('upload', $config);
            //load the excel library
            $this->load->library('excel');
             
            //read file from path
            $objPHPExcel = PHPExcel_IOFactory::load($path.$newname);
             
            //get only the Cell Collection
            // $cell_collection = $objPHPExcel->getActiveSheet()->getCellCollection();
            $cell_collection = $objPHPExcel->getActiveSheet()->toArray(null,true,true,true);

            $data = array();
            //extract to a PHP readable array format
            foreach ($cell_collection as $cell) {
                $sub_array = array();
                $sub_array['id'] = $cell['A'];
                $sub_array['nama'] = $cell['B'];
                $sub_array['norm'] = $cell['C'];
                $sub_array['nopen'] = $cell['D'];
                $sub_array['tgl_lahir'] = $cell['E'];
                $sub_array['jenis_kelamin'] = $cell['F'];
                $sub_array['tgl_masuk'] = $cell['G'];

                $data[] = $sub_array;

                // echo $cell['A']."<br>";
                // $column = $objPHPExcel->getActiveSheet()->getCell($cell)->getColumn();
                // $row = $objPHPExcel->getActiveSheet()->getCell($cell)->getRow();
                // $data_value = $objPHPExcel->getActiveSheet()->getCell($cell)->getValue();
             
                //The header will/should be in row 1 only. of course, this can be modified to suit your need.
                // if ($row == 1) {
                //     $header[$row][$column] = $data_value;
                // } else {
                //     $arr_data[$row][$column] = $data_value;
                // }
            }
            echo json_encode($data);

             
            // send the data in an array format
            // $data['header'] = $header;
            // $data['values'] = $arr_data;
            // print_r($header);
        }
    }

    public function calendar(){
        $result = $this->Jadwal_model->calendar(FALSE);
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['title'] = $row -> DESKRIPSI . ' | ' . $row -> KUOTA;
            $sub_array['start'] = $row -> START;
            $sub_array['end'] = $row -> END;
            $sub_array['color'] = $row -> COLOR;

            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function generateDate(){
        $post = $this->input->post();

        $tglAwal = $this->input->post('bulanAwal')."-01";
        $tglAkhir = date("Y-m-t", strtotime($this->input->post('bulanAkhir')."-01"));

        $begin = new DateTime($tglAwal);
        $end = new DateTime($tglAkhir);
        $end = $end->modify( '+1 day' );

        $daterange     = new DatePeriod($begin, new DateInterval('P1D'), $end);

        $hari = array(
            "Sun" => "Minggu",
            "Mon" => "Senin",
            "Tue" => "Selasa",
            "Wed" => "Rabu",
            "Thu" => "Kamis",
            "Fri" => "Jumat",
            "Sat" => "Sabtu"
        );

        $data = array();
        foreach($daterange as $date){
            $sub_array = array();
            $daterange     = $date->format("Y-m-d");
            $datetime     = DateTime::createFromFormat('Y-m-d', $daterange);
            $day         = $datetime->format('D');

            $getJadwal =  $this->Jadwal_model->get_jadwal($this->input->post('dokter'), $this->input->post('ruangan'), date('Y-m-d', strtotime($daterange)))->row();

            $countJadwal = $this->Jadwal_model->get_jadwal($this->input->post('dokter'), $this->input->post('ruangan'), date('Y-m-d', strtotime($daterange)))->num_rows();

            $countLibur = $this->Jadwal_model->get_libur(date('Y-m-d', strtotime($daterange)))->num_rows();

            if (isset($post['hari'])) {
                foreach ($post['hari'] as $input) {
                    if($day == $input) {
                        $sub_array['id'] = $countJadwal > 0  ? $getJadwal -> ID : "";

                        $sub_array['text_tanggal'] = $hari[$day].' / '. date('d-m-Y', strtotime($daterange));
                        $sub_array['tanggal'] = $countJadwal > 0 || $countLibur > 0 ? "" : date('Y-m-d', strtotime($daterange));
                        $sub_array['color'] = $countJadwal > 0 || $countLibur > 0 ? "bgcolor='#FF6161'" : "";
                        $sub_array['batal'] = $countJadwal > 0 || $countLibur > 0 ? "batal" : "hapus-tanggal";
                        $sub_array['status'] = $countJadwal;
                        $data[] = $sub_array;
                    }
                }
            }
        }

        echo json_encode($data);
    }

    public function gateDate(){
        $tglAwal = $this->input->post('bulanAwal')."-01";
        $tglAkhir = date("Y-m-t", strtotime($this->input->post('bulanAkhir')."-01"));

        $begin = new DateTime($tglAwal);
        $end = new DateTime($tglAkhir);
        $end = $end->modify( '+1 day' );

        $daterange     = new DatePeriod($begin, new DateInterval('P1D'), $end);

        $hari = array(
            "Sun" => "Minggu",
            "Mon" => "Senin",
            "Tue" => "Selasa",
            "Wed" => "Rabu",
            "Thu" => "Kamis",
            "Fri" => "Jumat",
            "Sat" => "Sabtu"
        );

        $data = array();
        foreach($daterange as $date){
            $sub_array = array();
            $daterange     = $date->format("Y-m-d");
            $datetime     = DateTime::createFromFormat('Y-m-d', $daterange);
            $day         = $datetime->format('D');

            $getJadwal =  $this->Jadwal_model->get_jadwal($this->input->post('dokter'), $this->input->post('ruangan'), date('Y-m-d', strtotime($daterange)))->row();

            $countJadwal = $this->Jadwal_model->get_jadwal($this->input->post('dokter'), $this->input->post('ruangan'), date('Y-m-d', strtotime($daterange)))->num_rows();

            $today = date("Y-m-d");
            $one_day_before = date("Y-m-d", strtotime($daterange."-1 day"));
            $detail = $this->Jadwal_detail_model->get_by(['ID_JADWAL' => $getJadwal->ID, 'STATUS' => 1]);
            if($day == $this->input->post('hari') && $countJadwal != 0 && !($this->input->post('ruangan') == '105020201' && $today >= $one_day_before)) {
                $sub_array['id'] = $getJadwal -> ID;

                $sub_array['text_tanggal'] = date('Y-m-d', strtotime($daterange));
                $sub_array['tanggal'] = date('Y-m-d', strtotime($daterange));
                $sub_array['color'] = "";
                $sub_array['batal'] ="hapus-tanggal";
                $sub_array['jam'] = $getJadwal -> AWAL .' - '. $getJadwal -> AKHIR;
                $sub_array['kuota'] =''.$getJadwal -> KUOTA;
                $sub_array['jam_sore'] = $getJadwal -> AWAL_SORE .' - '. $getJadwal -> AKHIR_SORE;
                $sub_array['kuotasore'] = ''.$getJadwal -> KUOTASORE;
                $sub_array['detail'] = $detail;

                $sub_array['status'] = $countJadwal;
                $data[] = $sub_array;
            }
        }

        echo json_encode($data);
    }
}
