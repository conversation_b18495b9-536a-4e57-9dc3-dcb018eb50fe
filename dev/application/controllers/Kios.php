<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Kios extends Backend_Controller {
  function __construct() {
    parent::__construct();
    $this->load->model(array('Pasien_model','Jadwal_model','Perjanjian_model'));
  }

	public function index()
	{
		$this->site->view('kios');
	}

  public function auth(){
    if($this->input->post('norm') == 0){
      $result = array('status' => 204, 'message' => 'Pasien tidak di temukan.');
    }else{
      $result = $this->Pasien_model->auth();
    }
    
    echo json_encode($result);
  }

  public 	function form(){

    $data = array(
            'nomr'       =>  $this->input->post('nomr'),
            'nama'       =>  $this->input->post('nama'),    
            'nomor'       =>  $this->input->post('nomor'),    

            // 'birth'      =>  $this->input->post('tgl_lahir')
            );

    $this->site->view('kios/index',$data);
  }

  public function jadwal(){
    $result = $this->Jadwal_model->jadwal();
    $data = array();
    $hari = array(
        'Monday' => 'Senin',
        'Tuesday' => 'Selasa',
        'Wednesday' => 'Rabu',
        'Thursday' => 'Kamis',
        'Friday' => 'Jumat',
        'Saturday' => 'Sabtu',
        'Sunday' => 'Minggu',
    );
    foreach ($result as $row) {
        $sub_array = array();
        $sub_array['id'] = $row -> ID;
        $sub_array['tanggal'] = $row -> TANGGAL;
        $sub_array['day'] = $row -> DAY;
        $sub_array['waktu'] = $row -> WAKTU;
        $sub_array['jumlah'] = $row -> JUMLAH;
        $sub_array['kuota'] = $row -> KUOTA;
        $sub_array['waktu_sore'] = $row -> WAKTU_SORE;
        $sub_array['jumlah_sore'] = $row -> JUMLAH_SORE;
        $sub_array['kuota_sore'] = $row -> KUOTASORE;
        $sub_array['ruangan'] = $row -> DESKRIPSI;
        $sub_array['id_ruangan'] = $row -> ID_RUANGAN;
        $sub_array['color'] = $row -> COLOR == NULL ? '#218496' : $row -> COLOR;
        $sub_array['hari'] = $hari[$row -> HARI];
        $data[] = $sub_array;
    }
    echo json_encode($data);
  }

  public function cek(){
    $result = $this->Perjanjian_model->cek();
    echo json_encode($result);
  }

  public function create()
  {
    $result = $this->Perjanjian_model->cek();
    if($result['status'] == 200){
      if(isset($result['data'])){
        $status_sore = $result['data']['status_sore'];
        $_POST['statusSore'] = $status_sore;
      }
      $result = $this->Perjanjian_model->create();
    }

    echo json_encode($result);
  }

  public function batalPasien()
  {
    $result = $this->Perjanjian_model->batalPasien();
    echo json_encode($result);
  }
}
