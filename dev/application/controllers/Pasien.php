<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Pasien extends Backend_Controller{

    function __construct() {
        parent::__construct();
        $this->load->model(array('Pasien_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }

    }

    public  function index(){
        //echo "backend";
        $this->site->view('index');
    }

    public function auth(){
        $result = $this->Pasien_model->auth();
        
        echo json_encode($result);
    }

}