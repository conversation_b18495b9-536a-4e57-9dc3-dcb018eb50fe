<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class User extends Backend_Controller{

    function __construct() {
        parent::__construct();
        $this->load->model(array('User_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function index(){
        if(!in_array(4,$this->session->userdata('akses'))){
            $this->site->view('error_403');
        }else{
            $this->site->view('user/index');
        }
    }

    public function datatables(){
        $result = $this->User_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $row -> NAMA;
            $sub_array[] = $row -> MENU;
            $sub_array[] = $row -> FITUR;
            $sub_array[] = "<button type='submit' class='btn btn-info' id='lihat'  data-id='".$row -> ID."'><i class='fas fa-edit'></i></button>";

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->User_model->total_count(),
            "recordsFiltered"   => $this->User_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }

}
