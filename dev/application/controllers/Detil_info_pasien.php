<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Detil_info_pasien extends Backend_Controller{


    function __construct() {
        parent::__construct();
        $this->load->model(array('Detil_info_pasien_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function datatables(){
        $result = $this->Detil_info_pasien_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $row -> NAMAPASIEN . ' <b>['.$row -> NOMR.']</b>';
            $sub_array[] = $row -> STATUS === 'sent' ? '<span class="badge badge-success">terkirim</span>' : '<span class="badge badge-danger">gagal</span>';
            $sub_array[] = $row -> STATUS === 'sent' ? 'terkirim' : 'gagal';
            // $sub_array[] = '#';

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->Detil_info_pasien_model->total_count(),
            "recordsFiltered"   => $this->Detil_info_pasien_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }
}

