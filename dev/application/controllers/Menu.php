<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Menu extends Backend_Controller{


    function __construct() {
        parent::__construct();
        $this->load->model(array('Menu_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function role(){
        $role_menu = $this->Menu_model->menu();
        $data = array();
        foreach ($role_menu->result() as $main) {
            $sub_menu = $this->Menu_model->menu($main -> ID);
            $menu_array = array();
            if($sub_menu->num_rows() > 0){
                $menu_array['label'] = $main -> LABEL;
                $menu_array['icon'] = $main -> ICON;
                $menu_array['link'] = $main -> LINK;
                $subb = array();
                foreach ($sub_menu->result() as $sub) {
                    $sub_array = array();
                    $sub_menu = $this->Menu_model->menu($sub -> ID);
                    $sub_array['label'] = $sub -> LABEL;
                    $sub_array['icon'] = $sub -> ICON;
                    $sub_array['link'] = $sub -> LINK;
                    $subb[] = $sub_array;
                }
                $menu_array['sub'] = $subb;
            }else{
                $menu_array['label'] = $main -> LABEL;
                $menu_array['icon'] = $main -> ICON;
                $menu_array['link'] = $main -> LINK;
            }
            $data[] = $menu_array;
        }
        echo json_encode($data);
    }

    public function menu()
    {
        $result = $this->Menu_model->get_table(FALSE);
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['idm'] = $row -> IDM;
            $sub_array['text'] = $row -> LABEL;
            $sub_array['status'] = $row -> STATUS;

            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

}

