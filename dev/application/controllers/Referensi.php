<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON>i extends Backend_Controller{

	function __construct() {
        parent::__construct();
        $this->load->model(array('Referensi_model'));
    }

    public function ruangan()
    {
        $result = $this->Referensi_model->ruangan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
            );
        echo json_encode($data);
    }

    public function smf()
    {
        $result = $this->Referensi_model->smf();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
            );
        echo json_encode($data);
    }

    public function dokter_smf()
    {
        $result = $this->Referensi_model->dokter_smf();
        echo json_encode($result);
    }

    public function dokter()
    {
        $result = $this->Referensi_model->dokter();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DOKTER;
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
            );
        echo json_encode($data);
    }

    public function rencana()
    {
        $result = $this->Referensi_model->rencana();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
            );
        echo json_encode($data);
    }

    public function smf_dokter()
    {
        $smf = $this->Referensi_model->smf();

        $sub_array=array();
        $sub_array2=array();
        $response = array();
        $response["results"] = array();
        foreach ($smf as $row) {

            $sub_array['text'] = $row -> DESKRIPSI;
            $sub_array['children'] = array();
            
                $dokter = $this->Referensi_model->dokter($row -> ID);
                foreach ($dokter as $key) {
                    $sub_array2['id'] = $key -> ID;
                    $sub_array2['text'] = $key -> DOKTER;    
                    array_push($sub_array['children'], $sub_array2);
                }
            // $sub_array = $sub_array2;
            array_push($response["results"], $sub_array);    
            //$data[] = $sub_array;
        }
        
        echo json_encode($response);

        //echo "<pre>";echo json_encode($data);echo "<pre>";


    }

    // Radiologi
    public function pemeriksaan()
    {
        $result = $this->Referensi_model->pemeriksaan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function tindakanRadPerKelas()
    {
        $result = $this->Referensi_model->tindakanRadPerKelas();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> NAMA;
            // $sub_array['text'] = $row -> NAMA.' - '."Rp " . number_format($row -> TARIF,2,',','.').'/'.$row -> KELAS;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function tujuanPemeriksaan()
    {
        $result = $this->Referensi_model->tujuanPemeriksaan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    // Prosedur Diagnosis
    public function kamarProsedur()
    {
        $result = $this->Referensi_model->kamarProsedurKuota();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function kamarProsedurKuota()
    {
        $result = $this->Referensi_model->kamarProsedurKuota();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI . " - $row->JML_PASIEN / $row->KUOTA";
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function tindakanProsedur()
    {
        $result = $this->Referensi_model->tindakanProsedur();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> NAMA;
            // $sub_array['text'] = $row -> NAMA.' - '."Rp " . number_format($row -> TARIF,2,',','.').'/'.$row -> KELAS;
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function ruanganPelayanan()
    {
        $result = $this->Referensi_model->ruanganPelayanan();
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['text'] = $row -> DESKRIPSI;
            $data[] = $sub_array;
        }
        $output = array(
            "item" => $data
            );
        echo json_encode($data);
    }

}