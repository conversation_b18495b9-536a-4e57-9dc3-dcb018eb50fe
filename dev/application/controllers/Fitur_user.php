<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Fitur_user extends Backend_Controller{


    function __construct() {
        parent::__construct();
        $this->load->model(array('Fitur_user_model'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->Fitur_user_model->rules;
    			$this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $data =array();
                        $index = 0;
                        if(isset($post['fitur'])){
                            foreach ($post['fitur'] as $input) {
                                if($post['fitur'][$index] != ""){
                                    array_push($data, array(
                                        'ID_FITUR' => $post['fitur'][$index],
                                        'ID_USER' => $post['id'],
                                    ));
                                }
                            $index++;  
                            }
                            $this->Fitur_user_model->insert($data, TRUE);
                        }
                        $result = array('status' => 'success');
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
    		}else if($param == 'aktif'){
                $post = $this->input->post(NULL,TRUE);
                $data = array(
                    'ID_FITUR' => $post['idf'],
                    'ID_USER' => $post['idu'],
                    'STATUS' => $post['status'],
                    // 'DELETED_AT' => date('Y-m-d h:i:s')
                );
    			if(!empty($post['id'])){
    				$this->Fitur_user_model->update($data, array('ID' => $post['id']));
                    $result = array('status' => 'success');
    			}else{
                    if($this->Fitur_user_model->insert($data)){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
                }

    			echo json_encode($result);
    		}
    	}
    }
}

