<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Daftar extends Backend_Controller{

	function __construct(){
        parent::__construct();

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

	public function index(){
        if(!in_array(6,$this->session->userdata('akses'))){
			$this->site->view('error_403');
		}else{
            $this->site->view('radiologi/daftar/index');
		}
	}

}