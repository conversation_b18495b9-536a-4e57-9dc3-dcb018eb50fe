<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Menu_user extends Backend_Controller{


    function __construct() {
        parent::__construct();
        $this->load->model(array('Menu_user_model'));
    }

    public function action($param){
    	if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
    		if($param == 'tambah' || $param == 'ubah'){
    			$rules = $this->Menu_user_model->rules;
    			$this->form_validation->set_rules($rules);

    			if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();

                    $data =array();
                        $index = 0;
                        if(isset($post['menu'])){
                            foreach ($post['menu'] as $input) {
                                if($post['menu'][$index] != ""){
                                    array_push($data, array(
                                        'ID_MENU' => $post['menu'][$index],
                                        'ID_USER' => $post['id'],
                                    ));
                                }
                            $index++;  
                            }
                            $this->Menu_user_model->insert($data, TRUE);
                        }
                        $result = array('status' => 'success');
    			}else{
    				$result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
    			}
    			echo json_encode($result);
    		}else if($param == 'aktif'){
                $post = $this->input->post(NULL,TRUE);
                $data = array(
                    'ID_MENU' => $post['idm'],
                    'ID_USER' => $post['idu'],
                    'STATUS' => $post['status'],
                    // 'DELETED_AT' => date('Y-m-d h:i:s')
                );
    			if(!empty($post['id'])){
    				$this->Menu_user_model->update($data, array('ID' => $post['id']));
                    $result = array('status' => 'success');
    			}else{
                    if($this->Menu_user_model->insert($data)){
                        $result = array('status' => 'success');
                    }else{
                        $result = array('status' => 'failed');
                    }
                }

    			echo json_encode($result);
    		}
    	}
    }

    public function detail()
    {
        $result = $this->Menu_user_model->get_table(FALSE);
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['label'] = $row -> LABEL;

            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

}

