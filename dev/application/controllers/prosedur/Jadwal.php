<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Jadwal extends Backend_Controller{

    function __construct() {
        parent::__construct();
        $this->load->model(array('prosedur/Jadwal_model'));

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

    public function index(){
        if(!in_array(10,$this->session->userdata('akses'))){
            $this->site->view('error_403');
        }else{
            $this->site->view('prosedur/jadwal/index');
        }

    }

    public function view(){
        if(!in_array(10,$this->session->userdata('akses'))){
            $this->site->view('error_403');
        }else{
            $this->site->view('prosedur/jadwal/calendar');
        }
    }

    public function datatables(){
        $result = $this->Jadwal_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            // $sub_array[] = $row -> JENIS;
            $sub_array[] = $row -> ID;
            $sub_array[] = $row -> DESKRIPSI;
            $sub_array[] = $row -> STATUS == 1 ? '<i class="far fa-check-square fa-2x hapus" id="'.$row -> ID.'" jenis="'.$row -> JENIS.'"></i>' : '<i class="far fa-square fa-2x hapus" id="'.$row -> ID.'" jenis="'.$row -> JENIS.'"></i>';

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->Jadwal_model->total_count(),
            "recordsFiltered"   => $this->Jadwal_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }

    public function jadwal(){
        $result = $this->Jadwal_model->jadwal();
        $data = array();
        $hari = array(
            'Monday' => 'Senin',
            'Tuesday' => 'Selasa',
            'Wednesday' => 'Rabu',
            'Thursday' => 'Kamis',
            'Friday' => 'Jumat',
            'Saturday' => 'Sabtu',
            'Sunday' => 'Minggu',
        );
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['tanggal'] = $row -> TANGGAL;
            $sub_array['day'] = $row -> DAY;
            $sub_array['jumlah'] = $row -> JUMLAH;
            $sub_array['kuota'] = $row -> KUOTA;
            $sub_array['hari'] = $hari[$row -> HARI];
            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function action($param){
        if(!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest'){
            if($param == 'tambah' || $param == 'ubah'){
                $rules = $this->Jadwal_model->rules;
                $this->form_validation->set_rules($rules);
                $cek = $this->Jadwal_model->cek();

                if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    $data = array(
                            'ID_KAMAR' => $post['jkamar'],
                            'TANGGAL' => $post['jtanggal'],
                            'KUOTA' => $post['kuota'],
                            'OLEH' => $this->session->userdata('id'),
                        );
                    if(!empty($post['jid'])){
                        $this->Jadwal_model->update($data, array('ID' => $post['jid']));
                        $result = array('status' => 'success');
                    }else{
                        if($cek['status'] == 200){
                            if($this->Jadwal_model->insert($data)){
                                $result = array('status' => 'success');
                            }else{
                                $result = array('status' => 'failed');
                            }
                        }else{
                            $result = array('status' => 'failed','errors' => array('msg' => 'Data Sudah Ada!'));
                        }
                    }
                }else{
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            }else if($param == 'multiple'){
                $rules = $this->Jadwal_model->rules_multiple;
                $this->form_validation->set_rules($rules);
                // $cek = $this->Jadwal_model->cek();

                $data = array();
                $index = 0;
                if($this->form_validation->run() == TRUE){
                    $post = $this->input->post();
                    if (isset($post['jtanggal'])) {
                        foreach ($post['jtanggal'] as $input) {
                            if ($post['jtanggal'][$index] != "" ) {
                                array_push(
                                    $data, array(
                                        'ID_KAMAR' => $post['jkamar'][$index],
                                        'TANGGAL' => $post['jtanggal'][$index],
                                        'KUOTA' => $post['kuota'],
                                        'OLEH' => $this->session->userdata('id'),
                                    )
                                );
                            }
                            $index++;
                        }
                    }
                    if(!empty($post['jid'])){
                        $this->Jadwal_model->update($data, array('ID' => $post['jid']));
                        $result = array('status' => 'success');
                    }else{
                        $this->Jadwal_model->insert($data,true);
                        if($this->db->affected_rows() >  0){
                            $result = array('status' => 'success');
                        }else{
                            $result = array('status' => 'failed');
                        }
                        // if($cek['status'] == 200){
                        // }else{
                        //     $result = array('status' => 'failed','errors' => array('msg' => 'Data Sudah Ada!'));
                        // }
                    }
                }else{
                    $result = array('status' => 'failed', 'errors' => $this->form_validation->error_array());
                }
                echo json_encode($result);
            }else if($param == 'ambil'){
                $post = $this->input->post(NULL,TRUE);
                    $where = array(
                        'ID' => $post['id'],
                    );
                    echo json_encode(array(
                        'status' => 'success',
                        'data' => $this->Jadwal_model->get_table()
                    ));
            }else if($param == 'aktif'){
                $post = $this->input->post(NULL,TRUE);
                if(!empty($post['id'])){
                    $data = array(
                        // 'KETERANGAN' => $this->input->post('keterangan'),
                        'DELETED_AT' => date('Y-m-d h:i:s'),
                        'DELETED_BY' => $this->session->userdata('id'),
                        'STATUS' => '0',
                    );
                    $this->Jadwal_model->update($data, array('ID' => $post['id']));
                    $result = array('status' => 'success');
                }

                echo json_encode($result);
            }
        }
    }

    public function calendar(){
        $result = $this->Jadwal_model->calendar(FALSE);
        $data = array();
        foreach ($result as $row) {
            $sub_array = array();
            $sub_array['id'] = $row -> ID;
            $sub_array['title'] = $row -> DESKRIPSI . ' | ' . $row -> KUOTA;
            $sub_array['start'] = $row -> START;
            $sub_array['end'] = $row -> END;

            $data[] = $sub_array;
        }
        echo json_encode($data);
    }

    public function generateDate(){
        $tglAwal = $this->input->post('bulanAwal')."-01";
        $tglAkhir = date("Y-m-t", strtotime($this->input->post('bulanAkhir')."-01"));

        $begin = new DateTime($tglAwal);
        $end = new DateTime($tglAkhir);
        $end = $end->modify( '+1 day' );

        $daterange     = new DatePeriod($begin, new DateInterval('P1D'), $end);

        $hari = array(
            "Sun" => "Minggu",
            "Mon" => "Senin",
            "Tue" => "Selasa",
            "Wed" => "Rabu",
            "Thu" => "Kamis",
            "Fri" => "Jumat",
            "Sat" => "Sabtu"
        );

        $data = array();
        foreach($daterange as $date){
            $sub_array = array();
            $daterange     = $date->format("Y-m-d");
            $datetime     = DateTime::createFromFormat('Y-m-d', $daterange);
            $day         = $datetime->format('D');

            $getJadwal =  $this->Jadwal_model->get_jadwal($this->input->post('kamar'), date('Y-m-d', strtotime($daterange)))->row();

            $countJadwal = $this->Jadwal_model->get_jadwal($this->input->post('kamar'), date('Y-m-d', strtotime($daterange)))->num_rows();

            $countLibur = $this->Jadwal_model->get_libur(date('Y-m-d', strtotime($daterange)))->num_rows();


            // echo $countJadwal;

            if($day == $this->input->post('hari')) {
                // $sub_array['hari'] = $hari[$day];
                $sub_array['id'] = $countJadwal > 0  ? $getJadwal -> ID : "";

                $sub_array['text_tanggal'] = date('Y-m-d', strtotime($daterange));
                $sub_array['tanggal'] = $countJadwal > 0 || $countLibur > 0 ? "" : date('Y-m-d', strtotime($daterange));
                $sub_array['color'] = $countJadwal > 0 || $countLibur > 0 ? "bgcolor='#FF6161'" : "";
                $sub_array['batal'] = $countJadwal > 0 || $countLibur > 0 ? "batal" : "hapus-tanggal";
                $sub_array['status'] = $countJadwal;
                $data[] = $sub_array;
            }
        }

        echo json_encode($data);
    }
}
