<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON><PERSON> extends Backend_Controller{

	function __construct() {
        parent::__construct();
        $this->load->model(array('prosedur/Perjanjian_model'));
        $this->load->library('whatsapp');

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

	public 	function index(){
        $data = array(
                    'nomr'       =>  $this->input->post('nomr'),
                    'nama'       =>  $this->input->post('nama'),    
                    'nomor'       =>  $this->input->post('nomor'),    

                    // 'birth'      =>  $this->input->post('tgl_lahir')
                );

                if(!in_array(8,$this->session->userdata('akses'))){
                    $this->site->view('error_403');
                }else{
                    $this->site->view('prosedur/perjanjian/index',$data);
                }
	}

    public function datatables(){
        $result = $this->Per<PERSON>jian_model->datatables();
        $data = array();
        foreach ($result as $row){
            $sub_array = array();
            $sub_array[] = $row -> ID;
            $sub_array[] = $row -> NAMAPASIEN."<b> [ ".$row -> NOMR." ]</b>";
            $sub_array[] = $row -> TANGGAL;
            $sub_array[] = $row -> TINDAKAN;
            $sub_array[] = $row -> DESKRIPSI;
            $sub_array[] = $row -> JAM;
            $sub_array[] = $row -> RUANGAN;
            $sub_array[] = $row -> DOKTER;

            $data[] = $sub_array;
        }

        $output = array(
            "draw"              => intval($_POST["draw"]),
            "recordsTotal"      => $this->Perjanjian_model->total_count(),
            "recordsFiltered"   => $this->Perjanjian_model->filter_count(),
            "data"               => $data
        );
        echo json_encode($output);
    }

    public function create()
    {
        $result = $this->Perjanjian_model->cek();
        if($result['status'] == 200){
            $result = $this->Perjanjian_model->create();
        }

        echo json_encode($result);
    }

    public function batalPasien()
    {
        $result = $this->Perjanjian_model->batalPasien();
        echo json_encode($result);
    }

    public function cek(){
        $result = $this->Perjanjian_model->cek();
        echo json_encode($result);
    }

    public function pasienKamar(){
        $result = $this->Perjanjian_model->pasienKamar();
                    
        echo json_encode(array(
            'status' => 'success',
            'data' => $result
        ));
    }

    public function cekDokter(){
        $result = $this->Perjanjian_model->cekDokter();
        echo json_encode($result);
    }
}