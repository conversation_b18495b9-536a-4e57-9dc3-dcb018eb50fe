<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Dashboard extends Backend_Controller{

	function __construct(){
        parent::__construct();

        if($this->session->userdata('logged_in') == false ){
            redirect('login');
        }
    }

	public function index(){
		if(!in_array(8,$this->session->userdata('akses'))){
			$this->site->view('error_403');
		}else{			
			$this->site->view('prosedur/dashboard');
		}
	}

	public function signout(){
		unset($_SESSION);
        session_destroy();
        redirect('login');
	}

}