<?php
	
	function get_template_directory($path,$dir_file){
		global $SConfig;

		$replace_path = str_replace('\\', '/', $path);
		$document_root = str_replace('\\', '/', realpath($_SERVER['DOCUMENT_ROOT'])).'/';
		$get_digit_doc_root = strlen($document_root.$SConfig->_folder_application);
		$full_path = substr($replace_path,$get_digit_doc_root);
		return base_url().$full_path.'/'.$dir_file;
	}

	function get_template($view){
		$_this =& get_instance();
		return $_this->site->view($view);
	}

	function set_url($sub){
		$_this =& get_instance();
		if($_this->site->side == 'backend'){
			return $site_url('admin/',$sub);
		}
	}

	function is_active_page_print($page,$class){
		$_this =& get_instance();
		if($_this->site->side == 'backend' && $page == $_this->uri->segment(1)){
			return $class;
		}
	}

	function title(){
		$_this =& get_instance();
		global $SConfig;

		$array_backend_page = array(
				'dashboard' => 'Dashboard',
				'list' => 'List',
				'alat' => 'Alat',
				'referensi' => 'Referensi',
				'laporan' => 'Laporan',
				'login' => 'Login',
				'' => 'Login'
		);

		$title = NULL;
		if($_this->site->side == 'backend' && (array_key_exists($_this->uri->segment(1), $array_backend_page))){
			return $array_backend_page[$_this->uri->segment(1)];
		}
	}

	function themeColor(){
		$color = '#00a2b9';
		$status = isset($_SESSION['module']) ? $_SESSION['module'] : 1 ;
		if($status == 2){
			$color = '#2e3e56';
		}elseif($status == 3){
			$color = '#f96349';
		}
		return $color;
	}