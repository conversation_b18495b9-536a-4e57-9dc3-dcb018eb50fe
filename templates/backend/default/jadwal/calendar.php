<?php get_template('header');?>
    <main role="main" class="container" id="con">
	
		<div class="row">
	      	<div class="shadow-sm p-3 mb-5 col-sm-12 bg-white rounded">

		        <h4 style="font-weight: bold">JADWAL</h4>
		        <hr>
				<div id="massage">
				</div>
		        <div class="row">
					<div class="col-sm-12 col-md-12 col-xs-12 ">
						<div class="form-group row">
							<label class="col-sm-3 col-form-label"><strong> SMF</strong></label>
							<div class="col-sm-9">
								<select class="form-control filter" name="smf" id="valSmf" required style="width: 100%">
				        		</select>
							</div>
						</div>
					<div class="form-group row">
							<label class="col-sm-3 col-form-label"><strong> Dokter</strong></label>
							<div class="col-sm-9">
								<select class="form-control filter" name="dokter" id="valDokter" required style="width: 100%">
				        		</select>
							</div>
						</div>
					    <div id='calendar'></div>
	    			</div>
				</div>
		    </div>
		</div>
		<!-- Modal -->
		<div class="modal fade " id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="mt3">Form Tambah Jadwal</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						  <span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<form id="myForm" role="form" data-toggle="validator" autocomplete="off" accept-charset="utf-8">
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong>Lock Kiosk</strong></label>
								<div class="col-sm-9">
									<div class="form-check">
										<input class="form-check-input" type="checkbox" name="lock_kiosk" id="lockKiosk" value="1">
										<label class="form-check-label" for="lockKiosk">
											Kunci Kiosk 30 menit sebelum jadwal dimulai
										</label>
									</div>
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Tangal</strong></label>
								<div class="col-sm-9">
									<input type="text" class="form-control" name="txtWaktu" id="valWaktu" placeholder="[ TANGGAL ]" required readonly style="background: #FFC164; border:0px; box-shadow: none; font-weight: bold;border-radius: 10px;">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Ruangan</strong></label>
								<div class="col-sm-9">
									<select class="form-control filter" name="jruangan" id="valJruangan" readonly required style="width: 100%">
					        		</select>
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Jam</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="awal" id="valAwal" placeholder="[ Jam Mulai ]" min="0" max="23">
								</div>
								<label class="col-sm-1 col-form-label"><strong>-</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="akhir" id="valAkhir" placeholder="[ Jam Selesai ]" min="0" max="23">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong>Kuota</strong></label>
								<div class="col-sm-9">
									<input type="number" class="form-control" name="kuota" id="valKuota">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong></strong></label>
								<div class="col-sm-9">
									<div id="additionalTimes"></div>
									<button class="btn btn-success" id="addTime" type="button"><i class="fas fa-plus"></i> Tambah Jam</button>
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Jam Sore</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="awal_sore" id="valAwalSore" placeholder="[ Jam Mulai ]" min="0" max="23">
								</div>
								<label class="col-sm-1 col-form-label"><strong>-</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="akhir_sore" id="valAkhirSore" placeholder="[ Jam Selesai ]" min="0" max="23">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong>Kuota Sore</strong></label>
								<div class="col-sm-9">
									<input type="number" class="form-control" name="kuota_sore" id="valKuotaSore">
								</div>
							</div>
							<input type="text" class="form-control filter" name="jtanggal" id="valjTanggal" style="display: none" required>
							<input type="text" class="form-control" name="jdokter" id="valJdokter" style="display: none" readonly>
							<input type="text" class="form-control" name="jid" id="valJid" style="display: none" readonly>


    					</form>
					</div>
					<div class="modal-footer" style="background: #00a2b9;border-bottom-right-radius: 5px;border-bottom-left-radius: 5px;">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
						<button type="button" class="btn btn-primary" id="btnAddJadwal">Ya</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Modal Konfirmasi Hapus Slot -->
		<div class="modal fade" id="modalHapusSlot" tabindex="-1" role="dialog" aria-labelledby="modalHapusSlotTitle" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="modalHapusSlotTitle">Konfirmasi Hapus Slot</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<div id="pesanHapusSlot"></div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
						<button type="button" class="btn btn-danger" id="btnKonfirmasiHapus">Ya, Hapus</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Modal Transfer Slot -->
		<div class="modal fade" id="modalTransferSlot" tabindex="-1" role="dialog" aria-labelledby="modalTransferSlotTitle" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="modalTransferSlotTitle">Transfer Perjanjian ke Slot Lain</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<div class="alert alert-warning">
							<i class="fas fa-exclamation-triangle"></i>
							<span id="pesanTransfer"></span>
						</div>
						<div class="form-group">
							<label for="pilihSlotTujuan"><strong>Pilih Slot Tujuan:</strong></label>
							<select class="form-control" id="pilihSlotTujuan" required>
								<option value="">-- Pilih Slot --</option>
							</select>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
						<button type="button" class="btn btn-primary" id="btnKonfirmasiTransfer">Transfer & Hapus</button>
					</div>
				</div>
			</div>
		</div>
	</main>
<?php get_template('footer');?>
		
		<script>

			function calendar(id){
				$('#calendar').fullCalendar({
			        //selectable: true,
			        themeSystem: 'bootstrap4',
			        header: {
			          left: 'prev,next today',
			          center: 'title',
			          right: 'month,agendaWeek,agendaDay'
			        },
			        locale: 'id',
			        eventClick: function(eventObj) {
				        if (eventObj.url) {
				          alert(
				            'Clicked ' + eventObj.title + '.\n' +
				            'Will open ' + eventObj.url + ' in a new tab'
				          );

				          window.open(eventObj.url);

				          return false; // prevents browser from following link in current tab.
				        } else {
				          // alert('Clicked ' + eventObj.id);
							var id = eventObj.id;
							var detail = getJSON("<?=base_url('jadwal/action/ambil')?>",{id:id});
							var d = moment(detail.data['TANGGAL']);
							$('#myForm')[0].reset();
							$('#valJruangan').val(null).trigger('change');

							$('#valWaktu').val(d.format('DD MMMM YYYY'));

							//$('#valJruangan').val(detail.data['id']).trigger('change');
							// Set the value, creating a new option if necessary
							if ($('#valJruangan').find("option[value='" + detail.data['IDRUANGAN'] + "']").length) {
							    $('#valJruangan').val(detail.data['IDRUANGAN']).trigger('change');
							} else { 
							    // Create a DOM Option and pre-select by default
							    var newOption = new Option(detail.data['DESKRIPSI'], detail.data['IDRUANGAN'], true, true);
							    // Append it to the select
							    $('#valJruangan').append(newOption).trigger('change');
							}
							ruangan();
							
							$('#valJruangan').select2('destroy');
							$('#valJruangan').attr('readonly',true);
							
							$('#valSesi').select2('destroy');
							$('#valSesi').attr('readonly',true);

							$('#valjTanggal').val(detail.data['TANGGAL']);
							$('#valjTanggal').attr('readonly',true);
							$('#valAwal').val(detail.data['AWAL']);
							// $('#valAwal').attr('readonly',true);
							$('#valAkhir').val(detail.data['AKHIR']);
							// $('#valAkhir').attr('readonly',true);
							$('#valKuota').val(detail.data['KUOTA']);
							$('#valAwalSore').val(detail.data['AWAL_SORE']);
							$('#valAkhirSore').val(detail.data['AKHIR_SORE']);
							$('#valKuotaSore').val(detail.data['KUOTASORE']);
							$('#valJdokter').val(detail.data['DOKTER']);
							$('#valJid').val(detail.data['ID']);
							$('#lockKiosk').prop('checked', detail.data['LOCK'] == 1);

							var slot = getJSON("<?=base_url('jadwal/action/ambilDetail')?>",{id:id});
							if (slot.data.length > 0) {
								$('#additionalTimes').html('');
								$('#additionalTimes').show();

								$.each(slot.data,function(i,v){
									var newTimeInput = `
										<div class="input-group mb-3">
											<input type="text" class="form-control" name="id_detail[]" value="${v.ID}" style="display: none" readonly>
											<select class="form-control" name="detail_awal[]">
												${generateTimeOptions(v.AWAL.split(':')[0] + ':' + v.AWAL.split(':')[1])}
											</select>
											<select class="form-control" name="detail_akhir[]">
												${generateTimeOptions(v.AKHIR.split(':')[0] + ':' + v.AKHIR.split(':')[1])}
											</select>
											<input type="number" class="form-control" data-edit="1" name="detail_kuota[]" value="${v.KUOTA}" placeholder="[ Kuota ]">
											<div class="input-group-append">
												<button class="btn btn-danger hapus-slot" type="button" data-id="${v.ID}" title="Hapus Slot">
													<i class="fas fa-trash"></i>
												</button>
											</div>
										</div>`;
									$('#additionalTimes').append(newTimeInput);
								});
							} else {
								$('#additionalTimes').show();
								var newTimeInput = `
									<div class="input-group mb-3">
										<input type="text" class="form-control" name="id_detail[]" style="display: none" readonly>
										<select class="form-control" name="detail_awal[]">
											${generateTimeOptions()}
										</select>
										<select class="form-control" name="detail_akhir[]">
											${generateTimeOptions()}
										</select>
										<input type="number" class="form-control" data-edit="0" name="detail_kuota[]" placeholder="[ Kuota ]">
									</div>`;
								$('#additionalTimes').html(newTimeInput);
							}
							$('input[name="awal"], input[name="akhir"]').trigger('change');
							$('input[name="kuota"]').trigger('change');

							$('#myForm').attr('action','ubah');
							$("#mt3").html('Form Ubah Kuota');
							$("#myModal").modal('show');
				        }
				      },
			        events: {
				        url: "<?=base_url('jadwal/calendar/')?>"+id,
				        error: function() {
				          $('#script-warning').show();
				        }
				      },
			        dayClick: function(date) {
			          // alert('clicked ' + date.format('MMMM Do YYYY'));
						var dokter = $('#valDokter').val(); 

						$('#myForm')[0].reset();
						// $('#valJid').val('');
						ruangan();
						$('#valWaktu').val(date.format('DD MMMM YYYY'));
						$('#valjTanggal').val(date.format());
						$('#valJdokter').val(dokter);
						$('#valJruangan').val(null).trigger('change');
						$('#valAwal').attr('readonly',false);
						$('#valAkhir').attr('readonly',false);
						$('#myForm').attr('action','tambah');
			    		$("#mt3").html('Form Tambah Jadwal');
						$('#additionalTimes').hide();
						var newTimeInput = `
							<div class="input-group mb-3">
								<input type="text" class="form-control" name="id_detail[]" style="display: none" readonly>
								<select class="form-control" name="detail_awal[]">
									${generateTimeOptions()}
								</select>
								<select class="form-control" name="detail_akhir[]">
									${generateTimeOptions()}
								</select>
								<input type="number" class="form-control" data-edit="0" name="detail_kuota[]" placeholder="[ Kuota ]">
							</div>`;
						$('#additionalTimes').html(newTimeInput);
			          	$('#myModal').modal('show');
			        },
			        // select: function(startDate, endDate) {
			        //   alert('selected ' + startDate.format() + ' to ' + endDate.format());
			        // }
			    });
			}

		    $('#valDokter').select2({
		        placeholder: "[ Pilih Dokter ]",
		        allowClear: true,
		        ajax: {
		            url: '<?=base_url("referensi/dokter")?>',
		            dataType: 'json',
		            delay: 250,
		            processResults: function (data) {
		                return {
		                    results: data
		                };
		            },
		            cache: false
		        }
		    });

		    $('#valSmf').select2({
		        placeholder: "[ Pilih SMF ]",
		        allowClear: true,
		        ajax: {
		            url: '<?=base_url("referensi/smf")?>',
		            dataType: 'json',
		            delay: 250,
		            processResults: function (data) {
		                return {
		                    results: data
		                };
		            },
		            cache: false
		        }
		    });

		    $("#valSmf").change(function() {
		    	// alert($(this).val());
		    	var smf = $(this).val();
		    	if(smf == null){
		    		smf = "";
		    	}
			    $('#valDokter').select2({
			        placeholder: "[ Pilih Dokter ]",
			        allowClear: true,
			        ajax: {
			            url: '<?=base_url("referensi/dokter/")?>'+smf,
			            dataType: 'json',
			            delay: 250,
			            processResults: function (data) {
			                return {
			                    results: data
			                };
			            },
			            cache: false
			        }
			    });
		    });

		    $("#valDokter").change(function() {
		    	var id = $(this).val();
				$("#calendar").fullCalendar('destroy');
					if(id != null){
					calendar(id);
				}
	        });

	        function ruangan(){
				$('#valJruangan').select2({
			        placeholder: "[ Pilih Ruangan ]",
			        ajax: {
			            url: '<?=base_url("referensi/ruangan")?>',
			            dataType: 'json',
			            delay: 250,
			            processResults: function (data) {
			                return {
			                    results: data
			                };
			            },
			            cache: true
			        }
			    });
			}

		    $(document).on('click','#btnAddJadwal',function(){
				var action = $('#myForm').attr('action');
				var data = $('#myForm').serialize();
				$.ajax('<?=base_url();?>jadwal/action/'+action,{
					dataType: 'json',
					type: 'POST',
					data: data,
					success: function(data){
						if(data.status == 'success'){
							$("#myModal").modal('hide');
							toastr.success('Berhasil di Tambah');
							$("#calendar").fullCalendar('destroy');
							var id = $('#valJdokter').val();
							calendar(id);
						}
						else{
							$.each(data.errors, function(index, element){
								toastr.warning(element);
							});
						}
					},
					error: function(e){
						toastr.error('Terjadi Kesalahan !');
					}
				});
			});
			//Close submit

			$(document).on('change', 'input[name="awal"], input[name="akhir"], input[name="kuota"]', function() {
				var awal = $('input[name="awal"]').val();
				if (awal < 10) {
					awal = '0' + awal;
				}
				var akhir = $('input[name="akhir"]').val();
				if (akhir < 10) {
					akhir = '0' + akhir;
				}
				var kuota = $('input[name="kuota"]').val();

				if (awal && akhir) {
					$('#additionalTimes').show();
					$('select[name="detail_awal[]"], select[name="detail_akhir[]"], input[name="detail_kuota[]"]').each(function() {
						if ($(this).is('select')) {
							var hasSelected = false;
							$(this).find('option').each(function() {
								var optionValue = $(this).val();
								if (optionValue < awal + ':00' || optionValue > akhir + ':00') {
									$(this).prop('disabled', true).hide();
								} else {
									$(this).prop('disabled', false).show();
								}
								// Jika ada attribute selected, jadikan default
								if ($(this).is('[selected]')) {
									hasSelected = true;
								}
							});
							// Jika tidak ada selected, set default sesuai awal/akhir
							if (!hasSelected) {
								if ($(this).attr('name') === 'detail_awal[]') {
									$(this).val(awal + ':00');
								} else if ($(this).attr('name') === 'detail_akhir[]') {
									$(this).val(akhir + ':00');
								}
							}
						}
						else if ($(this).attr('name') === 'detail_kuota[]') {
							if ($(this).data('edit') == 0) {
								$(this).val(kuota);
							}
						}
					});
				} else {
					$('#additionalTimes').hide();
				}
			});

			$(document).on('change', 'input[name="kuota"], input[name="detail_kuota[]"]', function() {
				var kuota = $('input[name="kuota"]').val();
				// Validate total detail kuota
				var totalDetailKuota = 0;
				$('input[name="detail_kuota[]"]').each(function() {
					totalDetailKuota += parseInt($(this).val()) || 0;
				});
				if (totalDetailKuota !== parseInt(kuota)) {
					toastr.warning('Total detail kuota harus sama dengan kuota utama!');
					$('#btnAddJadwal').prop('disabled', true);
				} else {
					$('#btnAddJadwal').prop('disabled', false);
				}
			});

			$(document).on('click', '#addTime', function() {
				var newTimeInput = `
					<div class="input-group mb-3">
						<input type="text" class="form-control" name="id_detail[]" style="display: none" readonly>
						<select class="form-control" name="detail_awal[]">
							${generateTimeOptions()}
						</select>
						<select class="form-control" name="detail_akhir[]">
							${generateTimeOptions()}
						</select>
						<input type="number" class="form-control" data-edit="0" name="detail_kuota[]" placeholder="[ Kuota ]">
						<div class="input-group-append">
							<button class="btn btn-danger remove-time" type="button"><i class="fas fa-times"></i></button>
						</div>
					</div>`;
				$('#additionalTimes').append(newTimeInput);
				$('input[name="awal"], input[name="akhir"]').trigger('change');
				$('input[name="kuota"]').trigger('change');
			});

			$(document).on('click', '.remove-time', function() {
				$(this).closest('.input-group').remove();
				$('input[name="kuota"]').trigger('change');
			});

			function generateTimeOptions(selectedTime = null) {
				var options = '';
				for (var h = 0; h < 24; h++) {
					for (var m = 0; m < 60; m += 60) {
						var hour = h < 10 ? '0' + h : h;
						var minute = m < 10 ? '0' + m : m;
						var time = hour + ':' + minute;
						var selected = selectedTime === time ? ' selected' : '';
						options += '<option value="' + time + '"' + selected + '>' + time + '</option>';
					}
				}
				return options;
			}

			$('#additionalTimes').hide();

			// Event handler untuk tombol hapus slot
			$(document).on('click', '.hapus-slot', function() {
				var idSlot = $(this).data('id');
				var slotElement = $(this).closest('.input-group');

				// Simpan referensi untuk digunakan nanti
				window.currentSlotId = idSlot;
				window.currentSlotElement = slotElement;

				// Cek validasi slot
				$.ajax({
					url: '<?=base_url();?>jadwal/action/cekSlot',
					type: 'POST',
					dataType: 'json',
					data: { id_slot: idSlot },
					success: function(response) {
						if (response.status === false) {
							toastr.error(response.message);
							return;
						}

						if (response.slot_terisi) {
							// Slot sudah terisi, tampilkan modal transfer
							showModalTransfer(response);
						} else {
							// Slot kosong, tampilkan konfirmasi hapus langsung
							showModalHapus();
						}
					},
					error: function() {
						toastr.error('Terjadi kesalahan saat mengecek slot');
					}
				});
			});

			function showModalHapus() {
				$('#pesanHapusSlot').html('<p>Apakah Anda yakin ingin menghapus slot ini?</p>');
				$('#modalHapusSlot').modal('show');
			}

			function showModalTransfer(data) {
				var pesan = `Slot ini sudah terisi oleh ${data.jumlah_perjanjian} perjanjian. ` +
							`Anda perlu memindahkan perjanjian ke slot lain sebelum menghapus.`;
				$('#pesanTransfer').text(pesan);

				// Populate dropdown slot tujuan
				var options = '<option value="">-- Pilih Slot --</option>';
				$.each(data.slot_lain, function(i, slot) {
					var waktu = slot.AWAL + ' - ' + slot.AKHIR;
					options += `<option value="${slot.ID}">Slot ${waktu} (Kuota: ${slot.KUOTA})</option>`;
				});
				$('#pilihSlotTujuan').html(options);

				$('#modalTransferSlot').modal('show');
			}

			// Event handler untuk konfirmasi hapus
			$('#btnKonfirmasiHapus').click(function() {
				hapusSlot(window.currentSlotId, null);
			});

			// Event handler untuk konfirmasi transfer
			$('#btnKonfirmasiTransfer').click(function() {
				var slotTujuan = $('#pilihSlotTujuan').val();
				if (!slotTujuan) {
					toastr.warning('Pilih slot tujuan terlebih dahulu');
					return;
				}
				hapusSlot(window.currentSlotId, slotTujuan);
			});

			function hapusSlot(idSlot, idSlotTujuan) {
				var data = { id_slot: idSlot };
				if (idSlotTujuan) {
					data.id_slot_tujuan = idSlotTujuan;
				}

				$.ajax({
					url: '<?=base_url();?>jadwal/action/hapusSlot',
					type: 'POST',
					dataType: 'json',
					data: data,
					success: function(response) {
						if (response.status === 'success') {
							toastr.success(response.message);

							// Hapus elemen slot dari DOM
							window.currentSlotElement.remove();

							// Tutup modal
							$('#modalHapusSlot').modal('hide');
							$('#modalTransferSlot').modal('hide');

							// Trigger validasi kuota
							$('input[name="kuota"]').trigger('change');

						} else if (response.status === 'need_transfer') {
							// Tutup modal hapus dan tampilkan modal transfer
							$('#modalHapusSlot').modal('hide');
							showModalTransfer(response);

						} else {
							toastr.error(response.message);
						}
					},
					error: function() {
						toastr.error('Terjadi kesalahan saat menghapus slot');
					}
				});
			}
		</script>


