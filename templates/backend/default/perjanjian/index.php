		<div class="row">
            <div class="col-sm-12 col-md-8 col-xs-12 ">
                <div class="shadow-sm p-3 mb-5 col-sm-9 offset-sm-3 bg-white rounded">
                    <div class="float-right ">
                        <a class="nav-link" href="dashboard"><span class="badge badge-danger" style="font-size: 18px"><strong><i class="fas fa-chevron-left"></i> Kembali</strong></span></a>
                    </div>
                    <h1 style="font-weight: bold"><span class="badge badge-info">Form Perjanjian </span></h1>
                    <hr>
                    <div id="massage">
                    </div>
                    <div class="row">
                        <div class="col-sm-12 col-md-12 col-xs-12 ">
                            <form action="#" id="myForm" role="form" data-toggle="validator" method="post" autocomplete="off" accept-charset="utf-8">
																<input type="text" name="sore" id="valSore" value="0" class="d-none">
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label"><strong> No. RM</strong></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" name="norm" id="valNorm" value="<?=$nomr?>" readonly style="background: #FFC164; border:0px; box-shadow: none; font-weight: bold;border-radius: 10px;">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label"><strong> Nama</strong></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" name="nama" id="valNama" value="<?=$nama?>" readonly style="background: #FFC164; border:0px; box-shadow: none; font-weight: bold;border-radius: 10px;">
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label"><strong>No. Telepon</strong></label>
                                    <div class="col-sm-9">
                                        <input type="text" class="form-control" name="nomor" id="valNomor" value="<?=$nomor?>" readonly style="background: #FFC164; border:0px; box-shadow: none; font-weight: bold;border-radius: 10px;">
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label"><strong> Dokter</strong></label>
                                    <div class="col-sm-9">
                                        <select class="form-control filter" name="dokter" id="valDokter" required style="width: 100%">
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-sm-3 col-form-label rencana" style="display:none;"><strong> Rencana</strong></label>
                                    <div class="col-sm-9 rencana" style="display: none;">
                                        <select class="form-control filter" name="rencana" id="valRencana" required style="width: 100%">
                                        <option value="1">Konsultasi</option>
                                        </select>
                                    </div>
                                </div>
								<div class="form-group row">
									<label class="col-sm-3 col-form-label dpjp" style="display: none;"><strong>DPJP</strong></label>
									<div class="col-sm-9 dpjp" style="display: none;">
										<div class="form-check">
											<input name="dpjp" class="form-check-input" type="checkbox" value="1" id="dpjp" checked>
											<!-- <label class="form-check-label" for="dpjp">
												(di buatkan sebagai SEP)
											</label> -->
										</div>
									</div>
								</div>

                                <div class="operasi" style="display: none">
																	<div class="form-group row">
																			<label class="col-sm-3 col-form-label"><strong> Tujuan Operasi</strong></label>
																			<div class="col-sm-9">
																					<select class="form-control filter" name="tujuanOperasi" id="valTujuanOperasi" required style="width: 100%">
																					<option disabled selected>Pilih Tujuan Operasi</option>
																					<option value="1">Kuratif</option>
																					<option value="2">Paliatif</option>
																					<option value="3">Diagnostik</option>
																					</select>
																			</div>
																	</div>
																</div>
                                <div class="operasi" style="display: none">
                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label operasi" style="display:none;"><strong>Rencana Rawat Inap</strong></label>
                                        <div class="col-sm-9 operasi" style="display:none;">
                                            <div class="form-group">
                                                <input type="date" class="form-control filter" name="tanggalRawatInap" id="valTanggalRawatInap" required>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <label class="col-sm-3 col-form-label operasi" style="display:none;"></label>
                                        <div class="col-sm-4">
                                            <div class="form-group">
                                                <input type="text" class="form-control operasi" style="display:none;" name="diagnosa" id="valDiagnosa" placeholder="[ Diagnosa ]" required>
                                            </div>
                                        </div>
                                        <div class="col-sm-5">
                                            <div class="form-group">
                                                <input type="text" class="form-control operasi" style="display:none;" name="tindakan" id="valTindakan" placeholder="[ Tindakan Operasi ]" required></div>	
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row valTanggal">
                                    <label class="col-sm-3 col-form-label"></label>
                                    <div class="col-sm-3">
                                        <div class="form-group">
                                            <input type="text" class="form-control filter" name="tgl" id="valTanggal" placeholder="[ TANGGAL ]" required readonly style="background: #FFC164; border:0px; box-shadow: none; font-weight: bold;border-radius: 10px;">
                                        </div>
                                    </div>
                                    <div class="col-sm-6">
                                        <div class="form-group">
                                            <input type="text" class="form-control" id="valRuangan" placeholder="[ POLIKLINIK ]" required readonly style="background: #FFC164; border:0px; box-shadow: none; font-weight: bold;border-radius: 10px;">
                                            <input type="hidden" class="form-control" name="idr" id="valIdr" readonly>
                                            <input type="hidden" class="form-control" name="ids" id="valIds" readonly>
                                            <input type="hidden" class="form-control" name="idj" id="valIdj">
                                        </div>	
                                    </div>
                                </div>
																<div class="prosedur" style="display: none;">
																	<div class="form-group row">
																		<label class="col-sm-3 col-form-label"><strong> Kamar</strong></label>
																		<div class="col-sm-9">
																			<select class="form-control filter" name="kamar" id="valKamar" required style="width: 100%">
																					</select>
																		</div>
																	</div>
																	<div class="form-group row">
																		<label class="col-sm-3 col-form-label tindakan"><strong> Tindakan</strong></label>
																		<div class="col-sm-9 tindakanProsedur">
																			<select class="form-control" name="tindakanProsedur[]" id="valTindakanProsedur" required style="width: 100%" multiple="multiple">
																			</select>
																		</div>
																	</div>
																	<div class="form-group row">
																		<label class="col-sm-3 col-form-label"><strong>Asal Ruangan</strong></label>
																		<div class="col-sm-9">
																			<select class="form-control filter" name="ruangan" id="valRuanganAsal" style="width: 100%" required></select>
																		</div>
																	</div>
																	<div class="form-group row">
																		<label class="col-sm-3 col-form-label"><strong> Jam</strong></label>
																		<div class="col-sm-4">
																			<input type="number" min="0" max="24" class="form-control" name="valAwal" id="valAwal" placeholder="[ Jam Mulai ]">
																		</div>
																		<label class="col-sm-1 col-form-label"><strong>-</strong></label>
																		<div class="col-sm-4">
																			<input type="number" min="0" max="24" class="form-control" name="valAkhir" id="valAkhir" placeholder="[ Jam Selesai ]">
																		</div>
																	</div>
																</div>
                                <?php if(in_array(1,$this->session->userdata('fitur'))):?>
                                    <div class="form-group row">
                                        <div class="col-sm-9 offset-3 ">
                                            <div class="form-check">
                                                <input name="persetujuanInstalasi" class="form-check-input" type="checkbox" value="1" id="persetujuanInstalasi">
                                                <label class="form-check-label" for="persetujuanInstalasi">
                                                    Persetujuan Instalasi
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </form>
                            <div class="row">
                                <div class="offset-sm-3"></div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <button type="button" class="btn btn-info btn-lg btn-block" name="cari" id="cari"><i class="fas fa-search"></i> Cari</button>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <button type="button" class="btn btn-primary btn-lg btn-block" name="selesai" id="btnSelesai" disabled> Selesai</button>
                                    </div>
                                </div>
                                <!-- <div class="col-sm-4">
                                    <button type="button" class="btn btn-primary" name="selesai" id="btnSelesai" style="display: none;"> Selesai
                                    </button>
                                </div> -->
                            </div>
                        </div>
                    </div>
                </div>
		    </div>
            <?php if(in_array(2,$this->session->userdata('fitur')) && $nomr != 0): ?>
            <div class="col-sm-12 col-md-4 col-xs-12">
				<div class="shadow-sm p-3 mb-5 col-sm-12 bg-info rounded">
					<div class="d-flex justify-content-between align-items-center mb-3">
						<h4 style="font-weight: bold">Perjanjian Terakhir</h4>
						<button type="button" class="btn btn-light btn-sm" id="reloadHistoryBtn" title="Reload History">
							<i class="fas fa-sync-alt"></i>
						</button>
					</div>
					<hr>
					<ul class="list-group" id="lastHistory" style="overflow: auto; padding-right: 1px; max-height:400px;">
					</ul>
				</div>
			  </div>
            <?php endif; ?>
		</div>
		<!-- Modal -->
		<div class="modal fade " id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
			<div class="modal-dialog modal-lg" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="mt1">Pilih Tanggal</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						  <span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
							<div class="row">
								<?php if(in_array(3,$this->session->userdata('akses'))):?>
								<div class="col-sm-2 col">
									<div class="form-group">
									<button type="button" class="btn btn-info btn-lg btn-block" name="add" id="add"><i class="fas fa-plus"></i></button>
									</div>
								</div>
								<?php endif;?>				
								<div class="col-sm-1 col">
									<div class="form-group">
										<i class="fas fa-angle-left fa-3x prev" style="cursor: pointer;opacity: 0.6"></i>
									</div>
								</div>
								<div class="col-sm-8 col">
									<div class="form-group">
										<input class="form-control" type="month" name="bulan" id="bulan" placeholder="" style="background: #FFC164; border:0px; text-align: center; font-size: 24px ; box-shadow: none; font-weight: bold;" readonly>
									</div>
								</div>
								<div class="col-sm-1 col">
									<div class="form-group">
										<i class="fas fa-angle-right fa-3x next" style="cursor: pointer;opacity: 0.6"></i>
									</div>
								</div>
							</div>
							<div class="row" id="jad"></div>
					</div>
				</div>
			</div>
		</div>
		<!-- Modal -->
		<div class="modal fade " id="myModal2" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="exampleModalLongTitle">Konfirmasi</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						  <span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<div class="alert alert-warning d-none" role="alert" id="notifKuotaSore">
							Anda akan di masukan ke kuota sore, apakah anda setuju?
						</div>
						<p>Pastikan data yang anda input benar, tekan Ya jika ingin melanjutkan !</p>
						<!-- <br/> -->
						<div class="checkbox">
			                <label><input type="checkbox" class="cetak" required name="cetak" id="cetak"><i style="color: red"> Centang jika tidak ingin mencetak.</i></label>
			             </div>
					</div>
					<div class="modal-footer" style="background: #00a2b9;border-bottom-right-radius: 5px;border-bottom-left-radius: 5px;">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
						<button type="button" class="btn btn-primary" id="btnKonfirm">Ya</button>
					</div>
				</div>
			</div>
		</div>
		<!-- Modal -->
		<div class="modal fade " id="myModal3" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
			<div class="modal-dialog" role="document">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="mt3">Form Tambah Jadwal</h5>
						<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						  <span aria-hidden="true">&times;</span>
						</button>
					</div>
					<div class="modal-body">
						<form id="myForm2" role="form" data-toggle="validator" autocomplete="off" accept-charset="utf-8">
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Ruangan</strong></label>
								<div class="col-sm-9">
									<select class="form-control filter" name="jruangan" id="valJruangan" readonly required style="width: 100%">
					        		</select>
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong>Tanggal</strong></label>
								<div class="col-sm-9">
									<input type="date" class="form-control filter" name="jtanggal" id="valjTanggal" required>
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Jam</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="awal" id="valAwal" placeholder="[ Jam Mulai ]">
								</div>
								<label class="col-sm-1 col-form-label"><strong>-</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="akhir" id="valAkhir" placeholder="[ Jam Selesai ]">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong>Kuota</strong></label>
								<div class="col-sm-9">
									<input type="number" class="form-control" name="kuota" id="valKuota">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong></strong></label>
								<div class="col-sm-9">
									<div id="additionalTimes"></div>
									<button class="btn btn-success" id="addTime" type="button"><i class="fas fa-plus"></i> Tambah Jam</button>
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong> Jam Sore</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="awal_sore" id="valAwalSore" placeholder="[ Jam Mulai ]" min="0" max="23">
								</div>
								<label class="col-sm-1 col-form-label"><strong>-</strong></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" name="akhir_sore" id="valAkhirSore" placeholder="[ Jam Selesai ]" min="0" max="23">
								</div>
							</div>
							<div class="form-group row">
								<label class="col-sm-3 col-form-label"><strong>Kuota Sore</strong></label>
								<div class="col-sm-9">
									<input type="number" class="form-control" name="kuota_sore" id="valKuotaSore">
								</div>
							</div>
							<input type="text" class="form-control" name="jdokter" id="valJdokter" style="display: none;" readonly>
							<input type="text" class="form-control" name="jid" id="valJid" style="display: none;" readonly>


    					</form>
					</div>
					<div class="modal-footer" style="background: #00a2b9;border-bottom-right-radius: 5px;border-bottom-left-radius: 5px;">
						<button type="button" class="btn btn-secondary" data-dismiss="modal">Batal</button>
						<button type="button" class="btn btn-primary" id="btnAddJadwal">Ya</button>
					</div>
				</div>
			</div>
		</div>
		
		<script>
			var d = new Date(),n = ("0" + (d.getMonth()+1)).slice(-2),y = d.getFullYear();
			$('#bulan').val(y+"-"+n);

			$('.prev').hover(function(){
		        $(this).css("opacity", "1");
		        }, function(){
		        $(this).css("opacity", "0.6");
		    });
		    $('.next').hover(function(){
		        $(this).css("opacity", "1");
		        }, function(){
		        $(this).css("opacity", "0.6");
		    });

			$('.prev').click(function(){
				if(y+"-"+n != $('#bulan').val()){
					document.getElementById("bulan").stepDown();
					jadwal();
				}else{
					toastr.warning('Sudah Tidak Tersedia !');
				}
			});

			$('.next').click(function(){
				document.getElementById("bulan").stepUp();
				jadwal();
			});
			
			var norm = "<?= $nomr?>";
			if(norm == 0){
				$('#valNama').attr('readonly',false);
				$('#valNomor').attr('readonly',false);
				$('#valNama').removeAttr('style');
				$('#valNomor').removeAttr('style');
			}
			$('#valTujuanOperasi').select2();
			$('#cari').click(function () {
				$('.rencana').css('display','none');
				$('.dpjp').css('display','none');
				$('.operasi').css('display','none');
				$('#valRencana').select2("val","1");
				if($("#valDokter option:selected").length && $("#valNama").val() != "" && $("#valNomor").val() != ""){
		    		jadwal();
				}else{
					toastr.warning('Mohon Lengkapi Data !');
				}
	    		
			});

			$('#valJruangan').select2({
			        placeholder: "[ Pilih Ruangan ]",
			        ajax: {
			            url: '<?=base_url("referensi/ruangan")?>',
			            dataType: 'json',
			            delay: 250,
			            processResults: function (data) {
			                return {
			                    results: data
			                };
			            },
			            cache: true
			        }
			    });

			function ruangan(){
				$('#valJruangan').select2({
			        placeholder: "[ Pilih Ruangan ]",
			        ajax: {
			            url: '<?=base_url("referensi/ruangan")?>',
			            dataType: 'json',
			            delay: 250,
			            processResults: function (data) {
			                return {
			                    results: data
			                };
			            },
			            cache: true
			        }
			    });
			}
			kamar();
			function kamar(){
				var tanggal = $('#valTanggal').val();
				$('#valKamar').select2({
					placeholder: "[ Pilih Kamar ]",
					allowClear: true,
					ajax: {
						url: `<?=base_url("referensi/kamarProsedurKuota/")?>${tanggal}`,
						dataType: 'json',
						delay: 250,
						processResults: function (data) {
							return {
									results: data
							};
						},
						cache: false
					}
				});
			}

			$('#valRuanganAsal').select2({
				placeholder: "[ Pilih Ruangan ]",
				ajax: {
					url: '<?=base_url("referensi/ruanganPelayanan")?>',
					dataType: 'json',
					delay: 250,
					processResults: function (data) {
						return {
							results: data
						};
					},
					cache: true
				}
			});

			$('#valTindakanProsedur').select2({
				placeholder: "[ Pilih Tindakan ]",
				allowClear: true,
				ajax: {
					url: '<?=base_url("referensi/tindakanProsedur")?>',
					dataType: 'json',
					delay: 250,
					processResults: function (data) {
						return {
							results: data
						};
					},
					cache: false
				}
			});

			$('#valKamar').on('change', function(){
				var id = $(this).val();
				if(id != null){
					$.ajax({
						url : '<?=base_url("perjanjian/cekJadwalKamar")?>',
						type : 'post',
						data : {kamar : id, tanggal : $('#valTanggal').val()},
						dataType : 'json',
						success : function(data){
							if(data['status'] == 0){
								toastr.warning(data['message']);
								$('#valKamar').val(null).trigger('change');
								$('#valIdj').val('');
							}else{
								$('#valIdj').val(data['data']['ID']);
							}
						}
					});
				}
			});

			function jadwal(){
				$('#valIdj').val("");
				$('#valKamar').val(null).trigger('change');
				$('#valRuanganAsal').val(null).trigger('change');
				$('#valAwal').val('');
				$('#valAkhir').val('');
				var dokter = $('#valDokter').val(); 
				var norm = $('#valNorm').val(); 
				var smf = $('#valIds').val(); 
				var txtdokter = $('#valDokter').text();
				var bulan =  $('#bulan').val();
				var poli = "<?= in_array(3,$this->session->userdata('fitur')) ? 1 : 0;?>";
				$.ajax({
					url: 'jadwal/jadwal',
					type:"POST",
					data: {dokter:dokter,bulan:bulan,smf:smf,norm:norm,poli:poli},
					dataType:'JSON',
					success:function(data){
						$('#jad').html('');
						// $('#mt1').html('');

						$("#myModal").modal('show');
						
						if(data.length){
							$.each(data, function(index, element){
								var bg = "bg-info";
								if(element.id_ruangan == 105020201 || element.id_ruangan == 105020202){
									bg = "bg-success";
								}
								var cls = "tanggal";
								if(parseInt(element.jumlah) >= parseInt(element.kuota)){
									bg = "bg-danger";
									// cls = "penuh";
								}
								var kuota = element.kuota;
								if(kuota == -1){
									kuota = '~';
								}
								var pagi = '';
								var sore = '';
								var iconsore = '';
								var iconpagi = '';
								if(element.kuota !== null) {
									iconpagi = '<a class="link changeSchedule" data-status="2" data-id="'+element.id+'" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>'
								}
								if(element.kuota_sore !== null){
									sore = '<div class="d-none" id="areaSore'+element.id+'">'+
														'<div class="d-flex justify-content-between">'+
															'<div><h4><span class="badge badge-dark"><i class="fas fa-clock fa-sm"></i> '+element.waktu_sore+'</span></h4></div>'+
															'<div class=" mt-2">'+iconpagi+'</div>'+
															'<div><h4 class="card-title text-right"><span class="badge badge-dark">'+element.jumlah_sore+'/'+element.kuota_sore+'</span></h4></div>' +
														'</div>'+
														'<div class="card-body '+cls+' " tanggal="'+element.tanggal+'" ruangan="'+element.ruangan+'" idr="'+element.id_ruangan+'" sore="1" style="padding: 0;margin: 0; cursor: pointer;">'+
															'<p class="card-text text-center" style="font-size:42px;text-shadow: 5px 0px 15px #000000;color:#ffc559;font-weight: bold;"><u>'+element.day+'</u></p>'+
														'</div>'+
													'</div>';
									iconsore = '<a class="link changeSchedule" data-status="1" data-id="'+element.id+'" style="cursor: pointer;"><i class="fas fa-sync fa-sm"></i></a>';
								}

								if(element.kuota !== null) {
									pagi = '<div id="areaPagi'+element.id+'">'+
													'<div class="d-flex justify-content-between">'+
														'<div><h4><span class="badge badge-primary"><i class="fas fa-clock" style="font-size: 14px;"></i> '+element.waktu+'</span></h4></div>'+
														'<div class="mt-2">'+iconsore+'</div>'+
														'<div><h4 class="card-title text-right"><span class="badge badge-warning">'+element.jumlah+'/'+kuota+'</span></h4></div>' +
													'</div>'+
													'<div class="card-body '+cls+' " tanggal="'+element.tanggal+'" ruangan="'+element.ruangan+'" idr="'+element.id_ruangan+'" sore="0" style="padding: 0;margin: 0; cursor: pointer;">'+
														'<p class="card-text text-center" style="font-size:42px;text-shadow: 5px 0px 15px #000000;color:#ffc559;font-weight: bold;"><u>'+element.day+'</u></p>'+
													'</div>'+
												'</div>';
								}

								
								// $('#mt1').html('<i class="fas fa-user-md"> </i> '+txtdokter);
								if(element.kuota !== null || element.kuota_sore !== null){
									
									$('#jad').append(
										'<div class="col-md-3 mb-2">'+
											'<div class="card text-white" style="background-color:'+element.color+'">'+
												'<div class="card-header" style="font-size: 12px;color:#ffc559;font-weight: bold;">'+element.ruangan+'</div>'+
												pagi+
												sore+
												'<div class="card-footer text-center" style="font-size: 12px;color:#ffc559;font-weight: bold;">'+element.hari+'</div>'+
										'</div>');
										if(element.kuota !== null){
											if(element.jumlah == element.kuota && element.kuota_sore !== null){
												$('#areaPagi'+element.id).addClass('d-none');
												$('#areaSore'+element.id).removeClass('d-none');
											}else{
												$('#areaPagi'+element.id).removeClass('d-none');
												$('#areaSore'+element.id).addClass('d-none');
											}
										}else{
											$('#areaPagi'+element.id).addClass('d-none');
											$('#areaSore'+element.id).removeClass('d-none');
										}
								}
								});
								$('.changeSchedule').click(function(){
									var id = $(this).attr('data-id');
									var status = $(this).attr('data-status');
									if(status == 1){
										$('#areaPagi'+id).addClass('d-none');
										$('#areaSore'+id).removeClass('d-none');
									}else{
										$('#areaPagi'+id).removeClass('d-none');
										$('#areaSore'+id).addClass('d-none');
									}
									// console.log(id);
								});
						}else{
							// $('#mt1').html(txtdokter);	
							$('#jad').append('<div class="col-sm-12"><div class="alert alert-warning" role="alert">'+
											  'Tidak Ada Jadwal Dokter ... !'+
											'</div></div>');	
						}
					},
					error:function(){
						toastr.error('Terjadi Kesalahan !');
					}
				});
				// Close load jadwal
			}

			$(document).on('click','.tanggal',function(){
				var tanggal = $(this).attr('tanggal');
				var ruangan = $(this).attr('ruangan');
				var idr = $(this).attr('idr');
				var sore = $(this).attr('sore');
				$('#btnSelesai').attr('disabled',false);
				if(idr == '105020704' || idr == '105020705' || idr == '105060101' || idr == '105120101' || idr == '105110101' || idr == '105090101' || idr == '105020708' || idr == '105020103'){
					$('.rencana').css('display','block');
				}
				// if(idr == '105020708' || idr == '105020705'){
				// 	$('.dpjp').css('display','block');
				// }
				if(idr == '105090101'){
					$('.operasi').css('display','block');
          			$('#valRencana').val(null).trigger('click');
				}
				$('#valTanggal').val(tanggal);
				$('#valRuangan').val(ruangan);
				$('#valIdr').val(idr);
				$('#valSore').val(sore);
				$('#valRencana').select2({
					// placeholder: "[ Pilih Rencana ]",
					allowClear: true,
					ajax: {
						url: '<?=base_url("referensi/rencana/")?>'+$(this).attr('idr')+'/'+$('#valDokter').val(),
						dataType: 'json',
						delay: 250,
						processResults: function (data) {
							return {
								results: data
							};
						},
						cache: false
					}
				});
				kamar();
	    		$("#myModal").modal('hide');
			});

			$(document).on('click','.penuh',function(){
				swal("Kuota penuh!", "Silakan pilih hari lain.");
			});

			$("#valDokter").change(function() {
				var id = $(this).val();
				var detail = getJSON('referensi/dokter_smf',{id:id});

				if (detail && detail['SMF'] !== null) {
					$('#valIds').val(detail['SMF']);
				} else {
					$('#valIds').val('');
				}
				$('#btnSelesai').attr('disabled', true);
				$('#valTanggal').val("");
				$('#valRuangan').val("");
				$('#valIdr').val("");
			});

			$('#valDokter').select2({
		        placeholder: "[ Pilih Dokter ]",
		        allowClear: true,
		        ajax: {
		            url: '<?=base_url("referensi/dokter")?>',
		            dataType: 'json',
		            delay: 250,
		            processResults: function (data) {
		                return {
		                    results: data
		                };
		            },
		            cache: false
		        }
		    });

			$('#valRencana').select2({
		        // placeholder: "[ Pilih Rencana ]",
		        allowClear: true,
		        ajax: {
		            url: '<?=base_url("referensi/rencana/")?>'+$(this).attr('idr')+'/'+$('#valDokter').val(),
		            dataType: 'json',
		            delay: 250,
		            processResults: function (data) {
		                return {
		                    results: data
		                };
		            },
		            cache: false
		        }
		    });
			
			$('#valRencana').on('change', function(){
				var id = $(this).val();
				if(id == 12) {
					$('.prosedur').css('display','block');
				} else {
					$('.prosedur').css('display','none');
				}
			});


			$('#btnSelesai').click(function () {
				var form = $("#myForm").serialize();
				if($("#valRencana").val() == 12 && !$("#valKamar option:selected").length){
					toastr.warning('Mohon Lengkapi Data !');
					return false;
				}
				if($("#valDokter option:selected").length && $("#valNama").val() != "" && $("#valNomor").val() != "" && $("#valRencana option:selected").length){		    		
					$.ajax("<?= base_url('perjanjian/cek') ?>",{
						dataType:'json',
						type: 'POST',
						data: form,
						success: function(data){
							if(data.status == 503){
								swal({
									title: "Sudah Terdaftar!",
									text: data.info,
									icon: "warning",
									dangerMode: true,
									buttons: {
										cancel: "Kembali",
										// batal: {
										// 	text: "Batalkan",
										// 	value: "batal",
										// 	className: "btn-danger",
										// },
										// cetak: {
										// 	text: "Cetak",
										// 	value: "cetak",
										// 	className: "btn-info",
										// },
									},
								})
								.then((value) => {
									switch (value) {

										case "cetak":
											window.requestPrint({
												NAME:"aplikasi.perjanjian.CetakPerjanjian",
												TYPE:"Pdf", //Word
												EXT:"pdf", //docs
												PARAMETER:{
													ID:data.id
												},
												REQUEST_FOR_PRINT:false,//true = print lansung false = view
												PRINT_NAME:"CetakPerjanjian",
												CONNECTION_NUMBER:0,
												COPIES:1,
												id:"data.model.RequestReport-1"
											});
											break;

										case "batal":
											$.ajax("<?= base_url('perjanjian/batalPasien') ?>",{
												dataType:'json',
												type: 'POST',
												data: {id:data.id},
												success: function(data){
													if(data.status == 200){
														swal("Berhasil!", "Perjanjian dibatalkan!", "success")
															.then(() => {
																swal({
																	title: "Tetap di halaman ini?",
																	text: "Pilih Ya untuk tetap di halaman ini, atau Tidak untuk memuat ulang.",
																	icon: "info",
																	buttons: {
																		cancel: "Tidak",
																		confirm: {
																			text: "Ya",
																			value: true,
																			visible: true,
																			className: "",
																			closeModal: true
																		}
																	}
																}).then((stay) => {
																	if (!stay) {
																		location.reload();
																	}
																});
															});		
													}else {
														var massage = '<div class="alert alert-warning alert-dismissable>"'
																		+'<button type="button" class="close" data-dismiss="alert" aria-hidden="true"></button>'
																		+'<strong>Warning! </strong>'+data.message
																	+'</div>';

														$("#massage").html(massage);
													}
												},
												error: function(jqXHR, textStatus, errorThrown) {
													toastr.error('Terjadi Kesalahan !');
													console.log('jqXHR:');
													console.log(jqXHR);
													console.log('textStatus:');
													console.log(textStatus);
													console.log('errorThrown:');
													console.log(errorThrown);
												}
											});
											
											break;

										default:
											swal.close();
									}
								});
							}else if(data.status == 406){
								$("#myModal2").modal('hide');
								swal(data.message, "");
							}else{
								$("#myModal2").modal('show');
								$('#notifKuotaSore').addClass('d-none')
								console.log(data.id);
								if (data.id) {
									$('#dpjp').prop('disabled', true).prop('checked', false);
								} else {
									$('#dpjp').prop('disabled', false).prop('checked', true);
								}
								if(data.data['status_sore'] == 1){
									$('#notifKuotaSore').removeClass('d-none')
								}
							}
						},
						error: function(jqXHR, textStatus, errorThrown) {
							toastr.error('Terjadi Kesalahan !');    
							console.log('jqXHR:');
							console.log(jqXHR);
							console.log('textStatus:');
							console.log(textStatus);
							console.log('errorThrown:');
							console.log(errorThrown);
						}
					});
				}else{
					toastr.warning('Mohon Lengkapi Data !');
				}
			});

			$('#btnKonfirm').click(function () {
	    		var form = $("#myForm").serialize();
	    		var cetak = $('.cetak:checked').length;
	    		
    			var type = 'Word';
    			var ext = 'docs';
    			var print = true;
	    		if(cetak == 1){
	    			type = 'Pdf';
		    		ext = 'pdf';
		    		print = false;
	    		}

		    	$.ajax("<?= base_url('perjanjian/create') ?>",{
					dataType:'json',
					type: 'POST',
					data: form,
					success: function(data){
						if(data.status == 200){
							window.requestPrint({
								NAME:"aplikasi.perjanjian.CetakPerjanjian",
								TYPE:type, //Word
								EXT:ext, //docs
								PARAMETER:{
									ID:data.id
								},
								REQUEST_FOR_PRINT:print,//true = print lansung false = view
								PRINT_NAME:"CetakPerjanjian",
								CONNECTION_NUMBER:0,
								COPIES:1,
								id:"data.model.RequestReport-1"
							});
							$("#myModal2").modal('hide');
							swal("Berhasil!", "Perjanjian sudah dibuat!", "success")
								.then(() => {
									loadHistory();
									$('#valDokter').val(null).trigger('change');	
									if(data.data['status_prosedur'] == 1){
										$.ajax("<?= base_url('prosedur/perjanjian/create') ?>",{
											dataType:'json',
											type: 'POST',
											data: form,
											success: function(data2){
												if(data2.status == 200){
													$("#myModal2").modal('hide');
													swal("Berhasil!", "Perjanjian prosedur sudah dibuat!", "success")
													.then(() => {
														swal({
															title: "Tetap di halaman ini?",
															text: "Pilih Ya untuk tetap di halaman ini, atau Tidak untuk memuat ulang.",
															icon: "info",
															buttons: {
																cancel: "Tidak",
																confirm: {
																	text: "Ya",
																	value: true,
																	visible: true,
																	className: "",
																	closeModal: true
																}
															}
														}).then((stay) => {
															if (!stay) {
																location.reload();
															}
														});
													});
												}else if(data2.status == 503){
													$("#myModal2").modal('hide');
													swal("Gagal!", "Perjanjian prosedur sudah ada!", "warning")
													.then(() => {
														swal({
															title: "Tetap di halaman ini?",
															text: "Pilih Ya untuk tetap di halaman ini, atau Tidak untuk memuat ulang.",
															icon: "info",
															buttons: {
																cancel: "Tidak",
																confirm: {
																	text: "Ya",
																	value: true,
																	visible: true,
																	className: "",
																	closeModal: true
																}
															}
														}).then((stay) => {
															if (!stay) {
																location.reload();
															}
														});
													});
												}else if(data2.status == 406){
													$("#myModal2").modal('hide');
													swal(data2.massage, "Gagal membuat perjanjian prosedur.");
												}
											},
											error: function(jqXHR, textStatus, errorThrown) {
												toastr.error('Terjadi Kesalahan !');
												console.log('jqXHR:');
												console.log(jqXHR);
												console.log('textStatus:');
												console.log(textStatus);
												console.log('errorThrown:');
												console.log(errorThrown);
											}
										});
									}else{
										swal({
											title: "Tetap di halaman ini?",
											text: "Pilih Ya untuk tetap di halaman ini, atau Tidak untuk memuat ulang.",
											icon: "info",
											buttons: {
												cancel: "Tidak",
												confirm: {
													text: "Ya",
													value: true,
													visible: true,
													className: "",
													closeModal: true
												}
											}
										}).then((stay) => {
											if (!stay) {
												location.reload();
											}
										});
									}
								});		
						}else if(data.status == 503){
							$("#myModal2").modal('hide');

							swal({
								title: "Data sudah ada!",
								icon: "warning",
								dangerMode: true,
								buttons: {
									cancel: "Kembali",
									batal: {
										text: "Batalkan",
										value: "batal",
										className: "btn-danger",
									},
									cetak: {
										text: "Cetak",
										value: "cetak",
										className: "btn-info",
									},
								},
							})
							.then((value) => {
								switch (value) {

									case "cetak":
										window.requestPrint({
											NAME:"aplikasi.perjanjian.CetakPerjanjian",
											TYPE:"Pdf", //Word
											EXT:"pdf", //docs
											PARAMETER:{
												ID:data.id
											},
											REQUEST_FOR_PRINT:false,//true = print lansung false = view
											PRINT_NAME:"CetakPerjanjian",
											CONNECTION_NUMBER:0,
											COPIES:1,
											id:"data.model.RequestReport-1"
										});
										break;

									case "batal":
										$.ajax("<?= base_url('perjanjian/batalPasien') ?>",{
											dataType:'json',
											type: 'POST',
											data: {id:data.id},
											success: function(data){
												if(data.status == 'success'){
													swal("Berhasil!", "Perjanjian dibatalkan!", "success")
													.then(() => {
							    						location.reload();
													});
												}else {
													var massage = '<div class="alert alert-warning alert-dismissable>"'
								                					+'<button type="button" class="close" data-dismiss="alert" aria-hidden="true"></button>'
													                +'<strong>Warning! </strong>'+data.message
								            					+'</div>';

													$("#massage").html(massage);
												}
											},
											error: function(jqXHR, textStatus, errorThrown) {
												toastr.error('Terjadi Kesalahan !');
								                console.log('jqXHR:');
								                console.log(jqXHR);
								                console.log('textStatus:');
								                console.log(textStatus);
								                console.log('errorThrown:');
								                console.log(errorThrown);
								       		}
										});
										
										break;

									default:
										swal.close();
								}
							});
						}else if(data.status == 406){
							$("#myModal2").modal('hide');
							swal(data.massage, "Silahkan pilih hari lain.");
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						toastr.error('Terjadi Kesalahan !');
		                console.log('jqXHR:');
		                console.log(jqXHR);
		                console.log('textStatus:');
		                console.log(textStatus);
		                console.log('errorThrown:');
		                console.log(errorThrown);
		       		}
				});
			});

			$(document).on('click','#add',function(){
				$('#myForm2')[0].reset();
				var dokter = $('#valDokter').val(); 
				$('#valJdokter').val(dokter);
	            ruangan();
				// if($('#valIds').val() == 38){
				// 	$('#valKuota').val('30');
				// 	$('#valKuota').attr('readonly',true);
				// }else if($('#valIds').val() == 39){
				// 	$('#valKuota').val('26');
				// 	$('#valKuota').attr('readonly',true);
				// }else{
				// 	$('#valKuota').attr('readonly',false);
				// }
				$('#valJruangan').val(null).trigger('change');
				$('#valjTanggal').attr('readonly',false);
				$('#valAwal').attr('readonly',false);
				$('#valAkhir').attr('readonly',false);
				$('#additionalTimes').hide();
				var newTimeInput = `
					<div class="input-group mb-3">
						<input type="text" class="form-control" name="id_detail[]" style="display: none" readonly>
						<select class="form-control" name="detail_awal[]">
							${generateTimeOptions()}
						</select>
						<select class="form-control" name="detail_akhir[]">
							${generateTimeOptions()}
						</select>
						<input type="number" class="form-control" name="detail_kuota[]" placeholder="[ Kuota ]">
					</div>`;
				$('#additionalTimes').html(newTimeInput);
				$('#myForm2').attr('action','tambah');
	    		$("#mt3").html('Form Tambah Jadwal');
	    		$("#myModal3").modal('show');
			});

			$(document).on('click','.edit',function(){
				var id = $(this).attr('data');
			    var detail = getJSON('jadwal/action/ambil',{id:id});

				$('#myForm2')[0].reset();
				$('#valJruangan').val(null).trigger('change');

				if($('#valIds').val() == 38 || $('#valIds').val() == 39){
					$('#valKuota').attr('readonly',true);
				}else{
					$('#valKuota').attr('readonly',false);
				}
                //$('#valJruangan').val(detail.data['id']).trigger('change');
                // Set the value, creating a new option if necessary
	            if ($('#valJruangan').find("option[value='" + detail.data['IDRUANGAN'] + "']").length) {
	                $('#valJruangan').val(detail.data['IDRUANGAN']).trigger('change');
	            } else { 
	                // Create a DOM Option and pre-select by default
	                var newOption = new Option(detail.data['DESKRIPSI'], detail.data['IDRUANGAN'], true, true);
	                // Append it to the select
	                $('#valJruangan').append(newOption).trigger('change');
	            }
	            ruangan();
	            $('#valJruangan').select2('destroy');
	            $('#valJruangan').attr('readonly',true);
	            // $("#valJruangan").attr('disabled',true);
				$('#valKuota').val(detail.data['KUOTA']);
				$('#valjTanggal').val(detail.data['TANGGAL']);
				$('#valjTanggal').attr('readonly',true);
				$('#valAwal').val(detail.data['AWAL']);
				$('#valAwal').attr('readonly',true);
				$('#valAkhir').val(detail.data['AKHIR']);
				$('#valAkhir').attr('readonly',true);
				$('#valKuota').val(detail.data['KUOTA']);
				$('#valJdokter').val(detail.data['DOKTER']);
				$('#valJid').val(detail.data['ID']);

				$('#myForm2').attr('action','ubah');
	    		$("#mt3").html('Form Ubah Kuota');
	    		$("#myModal3").modal('show');
			});
			//Open Submit
			$(document).on('click','#btnAddJadwal',function(){
				var action = $('#myForm2').attr('action');
				var data = $('#myForm2').serialize();
				$.ajax('jadwal/action/'+action,{
					dataType: 'json',
					type: 'POST',
					data: data,
					success: function(data){
						if(data.status == 'success'){
							$("#myModal3").modal('hide');
							toastr.success('Berhasil di Tambah');
							jadwal();
						}
						else{
							$.each(data.errors, function(index, element){
								toastr.warning(element);
							});
						}
					},
					error: function(e){
						toastr.error('Terjadi Kesalahan !');
					}
				});
			});
			//Close submit

			loadHistory();
			$(document).on('click', '#reloadHistoryBtn', function() {
				loadHistory();
			});
			function loadHistory() {
				var norm = $('#valNorm').val();
				if(norm != 0){
					var detail = getJSON('<?= base_url('perjanjian/getHistory') ?>',{norm:'<?=$nomr?>'});
					if(detail.data && detail.data.length){
						var status;
						var color = "list-group-item-success";
						var batal = "";
						$('#lastHistory').html('');
						$.each(detail.data, function(index, element){
							status = "Terdaftar";
							batal = "";
							color = "list-group-item-success";
							if(element.STATUS == 0){
								status = "Dibatalkan";
								color = "list-group-item-danger";
								batal = `<br/><small class="text-muted">Di Batalkan oleh:<b> `+element.DELETED_BY+`</b> / `+moment(element.DELETED_AT).format("LLL")+`</small>`;
							}
							if (element.DPJP == 1) {
								$('#lastHistory').append(`
									<li class="list-group-item `+color+`">
										<div class="d-flex w-100 justify-content-between">
											<h5 class="mb-1">`+moment(element.TANGGAL).format("LL")+`</h5>
											<small class="text-muted">`+status+`</small>
										</div>
										<p class="mb-1" style="font-size:12px">
											Perjanjian ke dokter <b>`+element.DOKTER+`</b> ruangan <b>`+element.RUANGAN+`</b>, rencana <b>`+element.RENCANA+`</b>
											<br>
											<label style="font-size:14px;">
												<i class="fas fa-check-square text-success"></i> DPJP
											</label>
										</p>
										<div class="mb-2">
											`+(element.STATUS == 1 ? `<button class="btn btn-info btn-sm btn-cetak-history" data-id="`+element.ID+`"><i class="fas fa-print"></i> Cetak</button>` : ``)+`
											`+(element.STATUS == 1 ? `<button class="btn btn-danger btn-sm btn-batal-history" data-id="`+element.ID+`"><i class="fas fa-times"></i> Batal</button>` : ``)+`
										</div>
										<small class="text-muted">Di Daftarkan oleh: <b>`+element.CREATED_BY+`</b> / `+moment(element.CREATED_AT).format("LLL")+`</small>`+batal+`
									</li>`);
							} else {
								$('#lastHistory').append(`
									<li class="list-group-item `+color+`">
										<div class="d-flex w-100 justify-content-between">
											<h5 class="mb-1">`+moment(element.TANGGAL).format("LL")+`</h5>
											<small class="text-muted">`+status+`</small>
										</div>
										<p class="mb-1" style="font-size:12px">
											Perjanjian ke dokter <b>`+element.DOKTER+`</b> ruangan <b>`+element.RUANGAN+`</b>, rencana <b>`+element.RENCANA+`</b>
											<br>
											<label style="font-size:14px;">
												<input type="checkbox" class="dpjp-history" data-idj="`+element.ID+`" data-norm="`+element.NOMR+`" data-tgl="`+element.TANGGAL+`" `+(element.STATUS != 1 ? 'disabled' : '')+`> DPJP
											</label>
										</p>
										<div class="mb-2">
											`+(element.STATUS == 1 ? `<button class="btn btn-info btn-sm btn-cetak-history" data-id="`+element.ID+`"><i class="fas fa-print"></i> Cetak</button>` : ``)+`
											`+(element.STATUS == 1 ? `<button class="btn btn-danger btn-sm btn-batal-history" data-id="`+element.ID+`"><i class="fas fa-times"></i> Batal</button>` : ``)+`
										</div>
										<small class="text-muted">Di Daftarkan oleh: <b>`+element.CREATED_BY+`</b> / `+moment(element.CREATED_AT).format("LLL")+`</small>`+batal+`
									</li>`);
							}

							// Tambahkan event handler tombol cetak dan batal di luar loop (hanya sekali)
							$(document).off('click', '.btn-cetak-history').on('click', '.btn-cetak-history', function() {
								var id = $(this).data('id');
								window.requestPrint({
									NAME: "aplikasi.perjanjian.CetakPerjanjian",
									TYPE: "Pdf",
									EXT: "pdf",
									PARAMETER: { ID: id },
									REQUEST_FOR_PRINT: false,
									PRINT_NAME: "CetakPerjanjian",
									CONNECTION_NUMBER: 0,
									COPIES: 1,
									id: "data.model.RequestReport-1"
								});
							});

							$(document).off('click', '.btn-batal-history').on('click', '.btn-batal-history', function() {
								var id = $(this).data('id');
								swal({
									title: "Batalkan Perjanjian?",
									text: "Apakah Anda yakin ingin membatalkan perjanjian ini?",
									icon: "warning",
									buttons: true,
									dangerMode: true,
								}).then((willDelete) => {
									if (willDelete) {
										$.ajax({
											url: '<?= base_url('perjanjian/batalPasien') ?>',
											type: 'POST',
											data: { id: id },
											dataType: 'json',
											success: function(data) {
												if (data.status == 200 || data.status == 'success') {
													toastr.success('Perjanjian berhasil dibatalkan');
													loadHistory();
												} else {
													toastr.warning(data.message || 'Gagal membatalkan perjanjian');
												}
											},
											error: function() {
												toastr.error('Terjadi kesalahan saat membatalkan perjanjian');
											}
										});
									}
								});
							});	
						});
					}else{
						$('#lastHistory').html('');
					}
				}
			}

			$(document).on('change', '.dpjp-history', function() {
				var checked = $(this).is(':checked') ? 1 : 0;
				var id = $(this).data('idj');
				var norm = $(this).data('norm');
				var tgl = $(this).data('tgl');
				var $checkbox = $(this);
				if (id) {
					$.ajax({
						url: '<?= base_url("perjanjian/updateDpjp") ?>',
						type: 'POST',
						data: { id: id, norm: norm, tgl: tgl, dpjp: checked },
						dataType: 'json',
						success: function(response) {
							if (response.status == 'success') {
								toastr.success('DPJP berhasil diperbarui');
								loadHistory();
							} else {
								toastr.warning(response.message || 'Gagal memperbarui DPJP');
								$checkbox.prop('checked', !$checkbox.is(':checked'));
							}
						},
						error: function() {
							toastr.error('Terjadi kesalahan saat memperbarui DPJP');
							$checkbox.prop('checked', !$checkbox.is(':checked'));
						}
					});
				}
			});
						
			$(document).on('change', 'input[name="awal"], input[name="akhir"], input[name="kuota"]', function() {
				var awal = $('input[name="awal"]').val();
				if (awal < 10) {
					awal = '0' + awal;
				}
				var akhir = $('input[name="akhir"]').val();
				if (akhir < 10) {
					akhir = '0' + akhir;
				}
				var kuota = $('input[name="kuota"]').val();
				if (awal && akhir) {
					$('#additionalTimes').show();
					$('select[name="detail_awal[]"], select[name="detail_akhir[]"], input[name="detail_kuota[]"]').each(function() {
						$(this).find('option').each(function() {
							var optionValue = $(this).val();
							if (optionValue < awal + ':00' || optionValue > akhir + ':00') {
								$(this).prop('disabled', true).hide();
							} else {
								$(this).prop('disabled', false).show();
							}
						});
						if ($(this).attr('name') === 'detail_awal[]') {
							console.log($(this).attr('name'), awal);
							$(this).val(awal + ':00');
						} else if ($(this).attr('name') === 'detail_akhir[]') {
							console.log($(this).attr('name'), akhir);
							$(this).val(akhir + ':00');
						} else if ($(this).attr('name') === 'detail_kuota[]') {
							console.log($(this).attr('name'), kuota);
							$(this).val(kuota);
						}
					});
				} else {
					$('#additionalTimes').hide();
				}
			});

			$(document).on('change', 'input[name="kuota"], input[name="detail_kuota[]"]', function() {
				var kuota = $('input[name="kuota"]').val();
				// Validate total detail kuota
				var totalDetailKuota = 0;
				$('input[name="detail_kuota[]"]').each(function() {
					totalDetailKuota += parseInt($(this).val()) || 0;
				});
				if (totalDetailKuota !== parseInt(kuota)) {
					toastr.warning('Total detail kuota harus sama dengan kuota utama!');
					$('#btnAddJadwal').prop('disabled', true);
				} else {
					$('#btnAddJadwal').prop('disabled', false);
				}
			});

			$(document).on('click', '#addTime', function() {
				var newTimeInput = `
					<div class="input-group mb-3">
						<input type="text" class="form-control" name="id_detail[]" style="display: none" readonly>
						<select class="form-control" name="detail_awal[]">
							${generateTimeOptions()}
						</select>
						<select class="form-control" name="detail_akhir[]">
							${generateTimeOptions()}
						</select>
						<input type="number" class="form-control" name="detail_kuota[]" placeholder="[ Kuota ]">
						<div class="input-group-append">
							<button class="btn btn-danger remove-time" type="button"><i class="fas fa-times"></i></button>
						</div>
					</div>`;
				$('#additionalTimes').append(newTimeInput);
				$('input[name="awal"], input[name="akhir"]').trigger('change');
				$('input[name="kuota"]').trigger('change');
			});

			$(document).on('click', '.remove-time', function() {
				$(this).closest('.input-group').remove();
				$('input[name="kuota"]').trigger('change');
			});

			function generateTimeOptions(selectedTime = null) {
				var options = '';
				console.log(selectedTime);
				for (var h = 0; h < 24; h++) {
					for (var m = 0; m < 60; m += 60) {
						var hour = h < 10 ? '0' + h : h;
						var minute = m < 10 ? '0' + m : m;
						var time = hour + ':' + minute;
						var selected = selectedTime === time ? ' selected' : '';
						options += '<option value="' + time + '"' + selected + '>' + time + '</option>';
						console.log(selectedTime , time);
					}
				}
				return options;
			}

			$('#additionalTimes').hide();
		</script>


